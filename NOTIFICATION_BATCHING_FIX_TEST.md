# Email Notification Batching Fix - Test Procedure

## Issue Summary
The email batching system was sending duplicate notifications because:
1. **Cutoff time calculation** used a fixed 3-minute window instead of accounting for HIGH urgency notifications (1-minute window)
2. This caused notifications to be processed in multiple overlapping batches instead of a single consolidated batch

## Fix Applied
1. **Fixed cutoff time calculation** in `NotificationBatchProcessor.processReadyBatches()`:
   - Changed from fixed `windowMinutes + 1` to dynamic calculation using minimum batching window (1 minute for HIGH urgency)
   - Added `isBatchReadyForProcessing()` method to validate batches based on their specific urgency levels

2. **Enhanced logging** for debugging:
   - Added detailed batch processing logs in `NotificationBatchProcessor`
   - Added batch window calculation logs in `NotificationBatchingService`
   - Added batch discovery logs in `NotificationBatchQueueRepository`

## Test Procedure

### Prerequisites
1. Ensure the application is running with the following configuration:
   ```properties
   app.notifications.batching.enabled=true
   app.notifications.batching.window-minutes=3
   app.notifications.batching.process-interval-seconds=180
   logging.level.com.collabhub.be=DEBUG
   ```

2. Ensure you have access to:
   - Email service (Mailhog on localhost:1025 for development)
   - Application logs
   - Database to verify notification queue entries

### Test Steps

#### Step 1: Create Test Scenario
1. **Login** to the application as a user
2. **Create a collaboration hub** with at least 2 participants
3. **Create 2 posts** in the hub
4. **Create 1 chat channel** in the hub

#### Step 2: Generate Notifications (within 1-2 minutes)
1. **Mention a user in post 1 comment**: 
   - Add a comment like "Hey @username, what do you think?"
   - This creates a `COMMENT_MENTION` notification (HIGH urgency, 1-minute window)

2. **Mention the same user in post 2 comment**:
   - Add a comment like "Also @username, please review this"
   - This creates another `COMMENT_MENTION` notification (HIGH urgency, 1-minute window)

3. **Mention the same user in chat**:
   - Send a chat message like "Hi @username, can you help?"
   - This creates a `CHAT_MENTION` notification (HIGH urgency, 1-minute window)

#### Step 3: Monitor Logs
Watch the application logs for the following patterns:

**Expected Log Sequence:**
```
DEBUG - Calculated batch window: now=2024-01-01T14:32:15, urgency=HIGH, windowMinutes=1, batchWindowStart=2024-01-01T14:32:00
DEBUG - Generated batch key: user_123_2024-01-01T14:32:00 for notification 1 (type: COMMENT_MENTION, urgency: HIGH, window: 2024-01-01T14:32:00)
DEBUG - Generated batch key: user_123_2024-01-01T14:32:00 for notification 2 (type: COMMENT_MENTION, urgency: HIGH, window: 2024-01-01T14:32:00)
DEBUG - Generated batch key: user_123_2024-01-01T14:32:00 for notification 3 (type: CHAT_MENTION, urgency: HIGH, window: 2024-01-01T14:32:00)

INFO  - BATCH DISCOVERY: Found 3 pending notifications grouped into 1 batches with cutoff_time=2024-01-01T14:34:00
DEBUG - BATCH DETAILS: batch_key=user_123_2024-01-01T14:32:00, notification_count=3, notification_ids=[1, 2, 3]

INFO  - BATCH PROCESSING START: batch_key=user_123_2024-01-01T14:32:00, recipient_type=internal_user, recipient=123, notification_count=3, types=[COMMENT_MENTION, CHAT_MENTION], urgencies=[HIGH], batch_windows=[2024-01-01T14:32:00]

INFO  - BATCHED EMAIL SENT: Successfully sent batched email to user 123 with 3 notifications (types: COMMENT_MENTION, CHAT_MENTION)
```

#### Step 4: Verify Email Delivery
1. **Check email inbox** (Mailhog UI at http://localhost:8025 for development)
2. **Verify only ONE email** is received containing all 3 notifications
3. **Verify email content** shows:
   - Subject: "3 new notifications from Collaboration Hub"
   - Body contains all 3 notifications grouped appropriately
   - No duplicate notifications

#### Step 5: Database Verification
Query the notification batch queue to verify proper processing:

```sql
SELECT 
    id,
    notification_type,
    urgency,
    batch_window_start,
    status,
    created_at,
    processed_at
FROM notification_batch_queue 
WHERE user_id = [target_user_id]
ORDER BY created_at DESC
LIMIT 10;
```

**Expected Results:**
- All 3 notifications should have the same `batch_window_start` timestamp
- All 3 notifications should have `status = 'SENT'`
- All 3 notifications should have the same `processed_at` timestamp

### Success Criteria
✅ **PASS**: Only one email received containing all 3 notifications
✅ **PASS**: All notifications have the same batch window start time
✅ **PASS**: All notifications processed in a single batch
✅ **PASS**: No duplicate notifications in email

❌ **FAIL**: Multiple emails received
❌ **FAIL**: Duplicate notifications in any email
❌ **FAIL**: Notifications processed in separate batches

### Troubleshooting

If the test fails, check:

1. **Timing Issues**: Ensure all mentions are created within the same minute
2. **Configuration**: Verify batching is enabled and configured correctly
3. **Logs**: Look for ERROR or WARN messages in batch processing
4. **Database**: Check if notifications are being queued properly

### Additional Test Cases

#### Test Case 2: Different Urgency Levels
1. Create notifications with different urgency levels (NORMAL vs HIGH)
2. Verify they are processed in separate batches due to different windows

#### Test Case 3: External Participants
1. Repeat the test with external participants (email-only users)
2. Verify batching works the same way for external users

## Files Modified
- `be/src/main/java/com/collabhub/be/modules/notifications/service/NotificationBatchProcessor.java`
- `be/src/main/java/com/collabhub/be/modules/notifications/service/NotificationBatchingService.java`
- `be/src/main/java/com/collabhub/be/modules/notifications/repository/NotificationBatchQueueRepository.java`
