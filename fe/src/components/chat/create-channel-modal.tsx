import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Plus, Users, Hash } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { useCreateChannel, useChatUtils } from '@/hooks/chat';
import { $api } from '@/lib/api/client';
import { useCurrentUser } from '@/contexts/auth-context';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { HubParticipantResponse } from '@/lib/types/api';

interface CreateChannelModalProps {
  hubId: number;
  isOpen: boolean;
  onClose: () => void;
  onChannelCreated?: (channelId: number) => void;
}

interface CreateChannelForm {
  name: string;
  description: string;
  participantIds: number[];
}

export const CreateChannelModal = React.memo<CreateChannelModalProps>(({
  hubId,
  isOpen,
  onClose,
  onChannelCreated
}) => {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();
  const currentUser = useCurrentUser();
  const [selectedParticipants, setSelectedParticipants] = useState<number[]>([]);
  const { getInitials, getRoleBadgeColor } = useChatUtils();

  const {
    data: membersResponse,
    isLoading: loadingMembers,
    error: membersError,
    refetch: refetchMembers
  } = $api.useQuery('get', '/api/hubs/{hubId}/participants', {
    params: {
      path: { hubId },
      query: {
        page: 0,
        size: 100, // Most hubs won't have more than 100 participants
      },
    },
  }, {
    enabled: isOpen && !!hubId,
    // Cache data for 1 minute (participant list changes less frequently)
    staleTime: 1 * 60 * 1000,
    // Enable automatic refetching on window focus for fresh status
    refetchOnWindowFocus: true,
  });

  const members = useMemo(() => membersResponse?.content || [], [membersResponse?.content]);
  const createChannelMutation = useCreateChannel();

  // Find current user in participants list - memoized to prevent recalculation
  const currentUserParticipant = useMemo(() =>
    members.find(member =>
      member.userId === currentUser?.id || member.email === currentUser?.email
    ), [members, currentUser?.id, currentUser?.email]
  );

  // Extract current user ID for stable reference in useEffect
  const currentUserParticipantId = useMemo(() =>
    currentUserParticipant?.id, [currentUserParticipant?.id]
  );

  // Sort participants: current user first, then others alphabetically - memoized
  const sortedMembers = useMemo(() =>
    [...members].sort((a, b) => {
      const isCurrentUserA = a.userId === currentUser?.id || a.email === currentUser?.email;
      const isCurrentUserB = b.userId === currentUser?.id || b.email === currentUser?.email;

      if (isCurrentUserA && !isCurrentUserB) return -1;
      if (!isCurrentUserA && isCurrentUserB) return 1;

      const nameA = a.name || a.email || '';
      const nameB = b.name || b.email || '';
      return nameA.localeCompare(nameB);
    }), [members, currentUser?.id, currentUser?.email]
  );

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm<CreateChannelForm>({
    defaultValues: {
      name: '',
      description: '',
      participantIds: []
    }
  });

  const handleClose = useCallback(() => {
    reset();
    setSelectedParticipants([]);
    onClose();
  }, [reset, onClose]);

  // Auto-select current user when modal opens - simplified approach
  useEffect(() => {
    if (isOpen) {
      // Reset and auto-select current user when modal opens
      const currentUserId = currentUserParticipantId;
      if (currentUserId) {
        setSelectedParticipants([currentUserId]);
      } else {
        setSelectedParticipants([]);
      }
    } else {
      // Reset when modal closes
      setSelectedParticipants([]);
    }
  }, [isOpen, currentUserParticipantId]);

  const handleParticipantToggle = useCallback((participantId: number) => {
    // Prevent deselecting the current user
    if (currentUserParticipantId && participantId === currentUserParticipantId) {
      return;
    }

    setSelectedParticipants(prev =>
      prev.includes(participantId)
        ? prev.filter(id => id !== participantId)
        : [...prev, participantId]
    );
  }, [currentUserParticipantId]);

  const onSubmit = async (data: CreateChannelForm) => {
    if (selectedParticipants.length === 0) {
      toast.error(t(keys.collaborationHubs.chat.noParticipantsSelected));
      return;
    }

    try {
      // Ensure current user is always included in participant list
      const participantIds = [...selectedParticipants];
      if (currentUserParticipant && !participantIds.includes(currentUserParticipant.id!)) {
        participantIds.unshift(currentUserParticipant.id!);
      }

      const response = await createChannelMutation.mutateAsync({
        params: { path: { hubId } },
        body: {
          name: data.name,
          description: data.description || undefined,
          participant_ids: participantIds
        }
      });

      if (onChannelCreated && response.id) {
        onChannelCreated(response.id);
      }

      handleClose();
    } catch (error) {
      console.error('Failed to create channel:', error);
      toast.error(t(keys.collaborationHubs.chat.failedToCreateChannel));
    }
  };

  // Removed duplicate utility functions - now using shared useChatUtils hook

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={cn(
        "max-w-2xl max-h-[90vh]",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
      )}>
        <DialogHeader className={cn(
          "pb-4",
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            "flex items-center gap-2",
            isMobile && "text-base"
          )}>
            <Plus className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
            {t(keys.collaborationHubs.chat.createCustomChannel)}
          </DialogTitle>
          <DialogDescription>
            Create a custom channel with selected participants for focused discussions.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <ScrollArea className={cn(
            "max-h-[60vh]",
            isMobile && "max-h-[calc(100vh-12rem)] px-4"
          )}>
            <div className="space-y-6 pr-4">
              {/* Channel Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  {t(keys.collaborationHubs.chat.channelName)}
                </Label>
                <Input
                  id="name"
                  {...register('name', { 
                    required: 'Channel name is required',
                    maxLength: { value: 255, message: 'Channel name must not exceed 255 characters' }
                  })}
                  required
                  placeholder={t(keys.collaborationHubs.chat.channelNamePlaceholder)}
                  className={errors.name ? 'border-destructive' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-destructive">{errors.name.message}</p>
                )}
              </div>

              {/* Channel Description */}
              <div className="space-y-2">
                <Label htmlFor="description">
                  {t(keys.collaborationHubs.chat.channelDescription)}
                </Label>
                <Textarea
                  id="description"
                  {...register('description', {
                    maxLength: { value: 500, message: 'Description must not exceed 500 characters' }
                  })}
                  placeholder={t(keys.collaborationHubs.chat.channelDescriptionPlaceholder)}
                  rows={3}
                  className={errors.description ? 'border-destructive' : ''}
                />
                {errors.description && (
                  <p className="text-sm text-destructive">{errors.description.message}</p>
                )}
              </div>

              {/* Participant Selection */}
              <div className="space-y-3">
                <Label className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  {t(keys.collaborationHubs.chat.selectParticipants)}
                </Label>
                
                {selectedParticipants.length > 0 && (
                  <p className="text-sm text-muted-foreground">
                    {t(keys.collaborationHubs.chat.participantsSelected, { count: selectedParticipants.length })}
                  </p>
                )}

                {loadingMembers ? (
                  <div className="space-y-2">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-center gap-3 p-2">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div className="flex-1 space-y-1">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-48" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : membersError ? (
                  <div className="text-center py-4">
                    <p className="text-sm text-destructive mb-2">
                      {t(keys.collaborationHubs.chat.failedToLoadMembers)}
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => refetchMembers()}
                    >
                      {t(keys.common.retry)}
                    </Button>
                  </div>
                ) : members.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground">
                      {t(keys.collaborationHubs.chat.noMembers)}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {sortedMembers.map((member: HubParticipantResponse) => {
                      const isSelected = selectedParticipants.includes(member.id!);
                      const isCurrentUser = member.userId === currentUser?.id || member.email === currentUser?.email;
                      const displayName = member.name || member.email || 'Unknown';

                      return (
                        <label
                          key={member.id}
                          htmlFor={`participant-${member.id}`}
                          className={cn(
                            "flex items-center gap-3 p-2 rounded-lg transition-colors",
                            isCurrentUser ? "bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800" : "hover:bg-muted/50",
                            !isCurrentUser && "cursor-pointer",
                            isSelected && !isCurrentUser && "bg-primary/10 border border-primary/20"
                          )}
                        >
                          <Checkbox
                            id={`participant-${member.id}`}
                            checked={isSelected}
                            disabled={isCurrentUser}
                            onCheckedChange={() => {
                              if (!isCurrentUser && member.id) {
                                handleParticipantToggle(member.id);
                              }
                            }}
                          />

                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="text-sm font-medium">
                              {getInitials(displayName)}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <p className="font-medium text-sm truncate">
                                {displayName}
                              </p>
                              {isCurrentUser && (
                                <Badge variant="outline" className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300">
                                  You
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground truncate">
                              {member.email}
                            </p>
                          </div>

                          <Badge className={getRoleBadgeColor(member.role)}>
                            {member.role?.replace('_', ' ')}
                          </Badge>
                        </label>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>

          {/* Actions */}
          <div className={cn(
            "flex justify-end gap-3 pt-4 border-t",
            isMobile && "px-4 pb-4"
          )}>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={createChannelMutation.isPending}
            >
              {t(keys.common.cancel)}
            </Button>
            <Button
              type="submit"
              disabled={!isValid || selectedParticipants.length === 0 || createChannelMutation.isPending}
            >
              {createChannelMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                  {t(keys.collaborationHubs.chat.creating)}
                </>
              ) : (
                t(keys.collaborationHubs.chat.create)
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
});

CreateChannelModal.displayName = 'CreateChannelModal';
