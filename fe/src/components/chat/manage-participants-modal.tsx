import React, { useState, useMemo, useCallback } from 'react';
import { UserPlus, UserMinus, Users, Search } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { useHubParticipants } from '@/hooks/collaboration-hubs';
import { useAddChannelParticipants, useRemoveChannelParticipants, useChatUtils, useChatChannel } from '@/hooks/chat';
import { useQueryClient } from '@tanstack/react-query';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { HubParticipantResponse, ChatChannelResponse } from '@/lib/types/api';

interface ManageParticipantsModalProps {
  hubId: number;
  channel: ChatChannelResponse;
  isOpen: boolean;
  onClose: () => void;
}

export const ManageParticipantsModal = React.memo<ManageParticipantsModalProps>(({
  hubId,
  channel,
  isOpen,
  onClose
}) => {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedToAdd, setSelectedToAdd] = useState<number[]>([]);
  const [selectedToRemove, setSelectedToRemove] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState('add');
  const { getInitials, getRoleBadgeColor } = useChatUtils();

  const { data: allMembersResponse, isLoading: loadingMembers } = useHubParticipants(
    hubId,
    { enabled: isOpen, size: 100 }
  );

  // Fetch fresh channel data to get up-to-date participant list
  // Use shorter staleTime to ensure immediate refetch after mutations
  const { data: freshChannelData, isLoading: loadingChannel } = useChatChannel(
    hubId,
    channel.id!,
    { enabled: isOpen, staleTime: 0 }
  );

  const queryClient = useQueryClient();
  const addParticipantsMutation = useAddChannelParticipants();
  const removeParticipantsMutation = useRemoveChannelParticipants();

  const allMembers = useMemo(() => allMembersResponse?.content || [], [allMembersResponse?.content]);

  // Get current channel participants from fresh data (fallback to prop if fresh data not available)
  const currentParticipants = useMemo(() => {
    const channelToUse = freshChannelData || channel;
    if (!channelToUse.participants) return [];
    return channelToUse.participants.map(p => p.id!);
  }, [freshChannelData, channel]);

  // Filter members for adding (not currently in channel)
  const availableToAdd = useMemo(() => {
    return allMembers.filter(member => !currentParticipants.includes(member.id!));
  }, [allMembers, currentParticipants]);

  // Filter members for removing (currently in channel, excluding creator)
  const availableToRemove = useMemo(() => {
    return allMembers.filter(member => 
      currentParticipants.includes(member.id!) && 
      member.id !== channel.created_by_participant_id
    );
  }, [allMembers, currentParticipants, channel.created_by_participant_id]);

  // Filter by search query
  const filteredToAdd = useMemo(() => {
    if (!searchQuery) return availableToAdd;
    const query = searchQuery.toLowerCase();
    return availableToAdd.filter(member => 
      member.name?.toLowerCase().includes(query) || 
      member.email?.toLowerCase().includes(query)
    );
  }, [availableToAdd, searchQuery]);

  const filteredToRemove = useMemo(() => {
    if (!searchQuery) return availableToRemove;
    const query = searchQuery.toLowerCase();
    return availableToRemove.filter(member => 
      member.name?.toLowerCase().includes(query) || 
      member.email?.toLowerCase().includes(query)
    );
  }, [availableToRemove, searchQuery]);

  const handleClose = useCallback(() => {
    setSearchQuery('');
    setSelectedToAdd([]);
    setSelectedToRemove([]);
    setActiveTab('add');
    onClose();
  }, [onClose]);

  // Memoized toggle functions to prevent infinite re-renders
  const handleToggleAdd = useCallback((id: number) => {
    setSelectedToAdd(prev =>
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  }, []);

  const handleToggleRemove = useCallback((id: number) => {
    setSelectedToRemove(prev =>
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  }, []);

  const handleAddParticipants = useCallback(async () => {
    if (selectedToAdd.length === 0) return;

    try {
      await addParticipantsMutation.mutateAsync({
        params: { path: { hubId, channelId: channel.id! } },
        body: { participant_ids: selectedToAdd }
      });

      // Force immediate refetch of channel data to ensure fresh participant list
      await queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats/{channelId}', { params: { path: { hubId, channelId: channel.id } } }],
      });

      toast.success(t(keys.collaborationHubs.chat.participantsAdded));
      setSelectedToAdd([]);
    } catch (__error) {
      toast.error(t(keys.collaborationHubs.chat.failedToUpdateParticipants));
    }
  }, [selectedToAdd, addParticipantsMutation, hubId, channel.id, t, keys, queryClient]);

  const handleRemoveParticipants = useCallback(async () => {
    if (selectedToRemove.length === 0) return;

    try {
      await removeParticipantsMutation.mutateAsync({
        params: { path: { hubId, channelId: channel.id! } },
        body: { participant_ids: selectedToRemove }
      });

      // Force immediate refetch of channel data to ensure fresh participant list
      await queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats/{channelId}', { params: { path: { hubId, channelId: channel.id } } }],
      });

      toast.success(t(keys.collaborationHubs.chat.participantsRemoved));
      setSelectedToRemove([]);
    } catch (__error) {
      toast.error(t(keys.collaborationHubs.chat.failedToUpdateParticipants));
    }
  }, [selectedToRemove, removeParticipantsMutation, hubId, channel.id, t, keys, queryClient]);

  // Removed duplicate utility functions - now using shared useChatUtils hook

  const renderParticipantList = useCallback((
    participants: HubParticipantResponse[],
    selectedIds: number[],
    onToggle: (id: number) => void,
    emptyMessage: string
  ) => {
    if (loadingMembers || loadingChannel) {
      return (
        <div className="space-y-2">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center gap-3 p-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex-1 space-y-1">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-48" />
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (participants.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-sm text-muted-foreground">{emptyMessage}</p>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {participants.map((member: HubParticipantResponse) => (
          <div 
            key={member.id}
            className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors"
          >
            <Checkbox
              id={`participant-${member.id}`}
              checked={selectedIds.includes(member.id!)}
              onCheckedChange={() => onToggle(member.id!)}
            />
            
            <Avatar className="h-10 w-10">
              <AvatarFallback className="text-sm font-medium">
                {getInitials(member.name || '')}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <p className="font-medium text-sm truncate">
                {member.name}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {member.email}
              </p>
            </div>
            
            <Badge className={getRoleBadgeColor(member.role)}>
              {member.role?.replace('_', ' ')}
            </Badge>
          </div>
        ))}
      </div>
    );
  }, [loadingMembers, getInitials, getRoleBadgeColor]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={cn(
        "max-w-2xl max-h-[90vh]",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
      )}>
        <DialogHeader className={cn(
          "pb-4",
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            "flex items-center gap-2",
            isMobile && "text-base"
          )}>
            <Users className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
            {t(keys.collaborationHubs.chat.manageParticipants)}
          </DialogTitle>
          <DialogDescription>
            Add or remove participants from {channel.name}
          </DialogDescription>
        </DialogHeader>

        <div className={cn("space-y-4", isMobile && "px-4")}>
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search participants..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="add" className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                {t(keys.collaborationHubs.chat.addParticipants)}
                {selectedToAdd.length > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {selectedToAdd.length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="remove" className="flex items-center gap-2">
                <UserMinus className="h-4 w-4" />
                {t(keys.collaborationHubs.chat.removeParticipants)}
                {selectedToRemove.length > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {selectedToRemove.length}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="add" className="mt-4">
              <ScrollArea className="h-80">
                {renderParticipantList(
                  filteredToAdd,
                  selectedToAdd,
                  handleToggleAdd,
                  "All hub participants are already in this channel"
                )}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="remove" className="mt-4">
              <ScrollArea className="h-80">
                {renderParticipantList(
                  filteredToRemove,
                  selectedToRemove,
                  handleToggleRemove,
                  "No participants can be removed from this channel"
                )}
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        {/* Action Buttons */}
        <div className={cn(
          "flex justify-between pt-4 border-t",
          isMobile && "px-4 pb-4"
        )}>
          <div className="flex gap-2">
            {/* Add Button - only show when on add tab and items selected */}
            {activeTab === 'add' && selectedToAdd.length > 0 && (
              <Button
                onClick={handleAddParticipants}
                disabled={addParticipantsMutation.isPending}
              >
                {addParticipantsMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                    Adding...
                  </>
                ) : (
                  <>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add {selectedToAdd.length} participant{selectedToAdd.length !== 1 ? 's' : ''}
                  </>
                )}
              </Button>
            )}

            {/* Remove Button - only show when on remove tab and items selected */}
            {activeTab === 'remove' && selectedToRemove.length > 0 && (
              <Button
                variant="destructive"
                onClick={handleRemoveParticipants}
                disabled={removeParticipantsMutation.isPending}
              >
                {removeParticipantsMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                    Removing...
                  </>
                ) : (
                  <>
                    <UserMinus className="h-4 w-4 mr-2" />
                    Remove {selectedToRemove.length} participant{selectedToRemove.length !== 1 ? 's' : ''}
                  </>
                )}
              </Button>
            )}
          </div>

          <Button variant="outline" onClick={handleClose}>
            {t(keys.common.close)}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
});

ManageParticipantsModal.displayName = 'ManageParticipantsModal';
