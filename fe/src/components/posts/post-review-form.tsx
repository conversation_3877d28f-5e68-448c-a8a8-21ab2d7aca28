import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { ChevronDown, ChevronUp, CheckCircle, AlertTriangle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { useSubmitReview } from "@/hooks/posts"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { toast } from "sonner"
import { cn } from "@/lib/utils"
import type { PostListItemResponse, PostResponse } from "@/lib/types/api"
import { PostReviewRequestStatus } from "@/lib/api/v1"

// Form validation schema
const reviewFormSchema = z.object({
  status: z.enum(['approved', 'rework'] as const),
  review_notes: z.string().max(2000, 'Review notes cannot exceed 2000 characters').optional(),
})

type ReviewFormData = z.infer<typeof reviewFormSchema>

// Utility type for extracting review-related data from either post type
interface PostReviewData {
  id: number
  reviewer_notes?: string
  can_review: boolean
  my_review_status?: string
}

// Type guard to check if post is PostListItemResponse
function isPostListItemResponse(post: PostResponse | PostListItemResponse): post is PostListItemResponse {
  return 'can_review' in post && 'my_review_status' in post
}

// Utility function to extract review data from either post type
function extractReviewData(post: PostResponse | PostListItemResponse): PostReviewData {
  if (isPostListItemResponse(post)) {
    // PostListItemResponse has direct can_review and my_review_status fields
    return {
      id: post.id || 0,
      reviewer_notes: post.reviewer_notes,
      can_review: post.can_review || false,
      my_review_status: post.my_review_status,
    }
  } else {
    // PostResponse has permissions object and is missing my_review_status
    return {
      id: post.id || 0,
      reviewer_notes: post.reviewer_notes,
      can_review: post.permissions?.can_review || false,
      my_review_status: undefined, // Missing from PostResponse - needs backend fix
    }
  }
}

interface PostReviewFormProps {
  post: PostResponse | PostListItemResponse
  className?: string
  compact?: boolean
}

export function PostReviewForm({ post, className }: PostReviewFormProps) {
  const { t, keys } = useTranslations()
  const submitReview = useSubmitReview()

  // Extract review data from either post type
  const reviewData = extractReviewData(post)

  // Determine if user has already reviewed this post
  const hasReviewed = reviewData.my_review_status && reviewData.my_review_status !== 'pending'

  // Expand by default if user hasn't reviewed yet, collapsed if already reviewed
  const [isExpanded, setIsExpanded] = useState(!hasReviewed)

  const form = useForm<ReviewFormData>({
    resolver: zodResolver(reviewFormSchema),
    defaultValues: {
      status: 'approved',
      review_notes: '',
    },
  })

  const watchedStatus = form.watch('status')
  const watchedNotes = form.watch('review_notes') || ''

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "approved": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "rework": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const getStatusLabel = (status?: string) => {
    switch (status) {
      case "pending": return t(keys.collaborationHubs.posts.review.statusPending)
      case "approved": return t(keys.collaborationHubs.posts.review.statusApproved)
      case "rework": return t(keys.collaborationHubs.posts.review.statusRework)
      default: return t(keys.collaborationHubs.posts.review.statusPending)
    }
  }

  const handleSubmit = async (data: ReviewFormData) => {
    if (!reviewData.id) return

    try {
      await submitReview.mutateAsync({
        params: { path: { postId: reviewData.id } },
        body: {
          status: data.status as PostReviewRequestStatus,
          review_notes: data.review_notes || undefined,
        },
      })

      toast.success(t(keys.collaborationHubs.posts.review.reviewSubmitted))
      setIsExpanded(false)
      form.reset()
    } catch (__error) {
      // Error handling for review submission
      toast.error(t(keys.collaborationHubs.posts.review.reviewFailed))
    }
  }

  const handleCancel = () => {
    setIsExpanded(false)
    form.reset()
  }

  // Show current review status if user has already reviewed
  // Note: my_review_status is missing from PostResponse - this is a backend limitation

  // Don't render if we can't extract review data or user can't review
  if (!reviewData || !reviewData.can_review) {
    return null
  }

  return (
    <div className={cn("border rounded-lg", className)}>
      {/* Collapsed Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-3 flex items-center justify-between hover:bg-muted/50 transition-colors"
      >
        <div className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">
            {hasReviewed
              ? t(keys.collaborationHubs.posts.review.updateReview)
              : t(keys.collaborationHubs.posts.review.reviewPost)
            }
          </span>
          {hasReviewed && (
            <Badge variant="outline" className={getStatusColor(reviewData.my_review_status)}>
              {getStatusLabel(reviewData.my_review_status)}
            </Badge>
          )}
        </div>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4 text-muted-foreground" />
        ) : (
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        )}
      </button>

      {/* Expanded Form */}
      {isExpanded && (
        <div className="border-t p-4 space-y-4">
          {/* Reviewer Notes from Post Creator */}
          {post.reviewer_notes && (
            <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                {t(keys.collaborationHubs.posts.review.notesFromCreator)}
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200 whitespace-pre-wrap">
                {post.reviewer_notes}
              </p>
            </div>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              {/* Status Selection */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">
                      {t(keys.collaborationHubs.posts.review.reviewDecision)}
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="flex flex-col space-y-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="approved" id="approved" />
                          <Label htmlFor="approved" className="flex items-center gap-2 cursor-pointer">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            {t(keys.collaborationHubs.posts.review.approve)}
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="rework" id="rework" />
                          <Label htmlFor="rework" className="flex items-center gap-2 cursor-pointer">
                            <AlertTriangle className="h-4 w-4 text-orange-600" />
                            {t(keys.collaborationHubs.posts.review.requestRework)}
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Review Notes */}
              <FormField
                control={form.control}
                name="review_notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">
                      {t(keys.collaborationHubs.posts.review.reviewNotes)}
                      {watchedStatus === 'rework' && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder={
                          watchedStatus === 'approved'
                            ? t(keys.collaborationHubs.posts.review.notesPlaceholderApprove)
                            : t(keys.collaborationHubs.posts.review.notesPlaceholderRework)
                        }
                        className="min-h-[80px] resize-none"
                        maxLength={2000}
                      />
                    </FormControl>
                    <div className="flex justify-between items-center text-xs text-muted-foreground">
                      <span>
                        {watchedStatus === 'rework' 
                          ? t(keys.collaborationHubs.posts.review.notesRequiredForRework)
                          : t(keys.collaborationHubs.posts.review.notesOptional)
                        }
                      </span>
                      <span className={cn(
                        watchedNotes.length > 1800 && "text-orange-600",
                        watchedNotes.length >= 2000 && "text-red-600"
                      )}>
                        {watchedNotes.length}/2000
                      </span>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button
                  type="submit"
                  disabled={submitReview.isPending || (watchedStatus === 'rework' && !watchedNotes.trim())}
                  className="flex-1"
                >
                  {submitReview.isPending
                    ? t(keys.collaborationHubs.posts.review.submitting)
                    : t(keys.collaborationHubs.posts.review.submitReview)
                  }
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={submitReview.isPending}
                >
                  {t(keys.common.cancel)}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      )}
    </div>
  )
}
