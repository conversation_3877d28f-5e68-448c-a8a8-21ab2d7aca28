import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { MediaUpload } from '../media-upload'

// Mock the presigned upload hook
vi.mock('@/hooks/posts', () => ({
  usePresignedUpload: () => ({
    uploadFiles: vi.fn(),
    isLoading: false,
    uploadProgress: new Map(),
  }),
}))

describe('MediaUpload Drag and Drop', () => {
  const mockOnChange = vi.fn()
  const defaultProps = {
    value: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
      'https://example.com/image3.jpg'
    ],
    onChange: mockOnChange,
    hubId: 1,
    maxFiles: 10,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders media items with drag handles when multiple items exist', () => {
    render(<MediaUpload {...defaultProps} />)
    
    // Should render all media items
    expect(screen.getAllByRole('img')).toHaveLength(3)
    
    // Should show drag handles (GripVertical icons) - they're in the DOM but hidden by opacity
    const dragHandles = document.querySelectorAll('[data-testid="drag-handle"]')
    expect(dragHandles).toHaveLength(3)
  })

  it('does not show drag handles when only one item exists', () => {
    render(<MediaUpload {...defaultProps} value={['https://example.com/image1.jpg']} />)
    
    // Should render one media item
    expect(screen.getAllByRole('img')).toHaveLength(1)
    
    // Should not show drag handles when only one item
    const dragHandles = document.querySelectorAll('[data-testid="drag-handle"]')
    expect(dragHandles).toHaveLength(0)
  })

  it('disables drag handles when component is disabled', () => {
    render(<MediaUpload {...defaultProps} disabled={true} />)
    
    // Should render media items
    expect(screen.getAllByRole('img')).toHaveLength(3)
    
    // Drag handles should not be present when disabled
    const dragHandles = document.querySelectorAll('[data-testid="drag-handle"]')
    expect(dragHandles).toHaveLength(0)
  })

  it('generates stable IDs for media items', () => {
    const { rerender } = render(<MediaUpload {...defaultProps} />)
    
    // Get initial IDs
    const initialItems = document.querySelectorAll('[data-sortable-id]')
    const initialIds = Array.from(initialItems).map(item => item.getAttribute('data-sortable-id'))
    
    // Re-render with same URLs
    rerender(<MediaUpload {...defaultProps} />)
    
    // IDs should remain the same
    const rerenderedItems = document.querySelectorAll('[data-sortable-id]')
    const rerenderedIds = Array.from(rerenderedItems).map(item => item.getAttribute('data-sortable-id'))
    
    expect(rerenderedIds).toEqual(initialIds)
  })
})
