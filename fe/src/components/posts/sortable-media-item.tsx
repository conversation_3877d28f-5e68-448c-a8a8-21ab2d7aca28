import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { X, Video, GripVertical, AlertCircle } from 'lucide-react'

interface MediaFile {
  id: string
  file?: File
  url: string
  type: 'image' | 'video'
  filename: string
  size: number
  isUploading?: boolean
  uploadProgress?: number
  error?: string
}

interface SortableMediaItemProps {
  media: MediaFile
  onRemove: (id: string) => void
  onRetry: (id: string) => void
  canReorder: boolean
}

export function SortableMediaItem({
  media,
  onRemove,
  onRetry,
  canReorder
}: SortableMediaItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: media.id,
    disabled: !canReorder || media.isUploading || !!media.error
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="relative group"
      data-sortable-id={media.id}
    >
      <div className="aspect-square rounded-lg overflow-hidden bg-muted">
        {media.type === 'image' ? (
          <img
            src={media.url}
            alt={media.filename}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Video className="h-8 w-8 text-muted-foreground" />
          </div>
        )}
        
        {/* Upload Progress Overlay */}
        {media.isUploading && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="text-center text-white">
              <Progress 
                value={media.uploadProgress || 0} 
                className="w-16 mb-2" 
              />
              <p className="text-xs">Uploading...</p>
            </div>
          </div>
        )}
        
        {/* Error Overlay */}
        {media.error && (
          <div className="absolute inset-0 bg-red-500/20 flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="h-6 w-6 text-red-500 mx-auto mb-1" />
              <Button
                size="sm"
                variant="outline"
                onClick={() => onRetry(media.id)}
                className="text-xs"
              >
                Retry
              </Button>
            </div>
          </div>
        )}
      </div>
      
      {/* Remove Button */}
      {!media.isUploading && (
        <Button
          type="button"
          variant="destructive"
          size="sm"
          className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={() => onRemove(media.id)}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
      
      {/* Drag Handle for Reordering */}
      {canReorder && !media.isUploading && !media.error && (
        <div
          className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity cursor-move"
          data-testid="drag-handle"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="h-4 w-4 text-white drop-shadow-lg" />
        </div>
      )}
      
      {/* File Info */}
      <div className="mt-2">
        <p className="text-xs text-muted-foreground truncate">
          {media.filename}
        </p>
        {media.error && (
          <p className="text-xs text-red-500 mt-1">
            {media.error}
          </p>
        )}
      </div>
    </div>
  )
}
