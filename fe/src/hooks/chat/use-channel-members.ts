import { $api } from '@/lib/api/client';
import type { HubParticipantResponse } from '@/lib/types/api';
import { useHubParticipants } from '@/hooks/collaboration-hubs';
import { ChatChannelResponseScope } from '@/lib/api/v1';
import { useMemo } from 'react';

/**
 * Custom hook for fetching channel members/participants.
 * Fetches the specific chat channel details and extracts the participants
 * who are actual members of that channel.
 *
 * For general channels: Returns all hub participants (since general channels include everyone)
 * For custom channels: Returns only the specific channel participants
 *
 * The backend automatically:
 * - Scopes participants to the current account (multi-tenancy)
 * - Validates user access to the hub and channel
 * - For general channels: returns participants = null (handled by fallback to hub participants)
 * - For custom channels: returns specific channel participants
 *
 * @param hubId - Hub ID
 * @param channelId - Channel ID
 * @param options - Query options including enabled and staleTime
 */
export function useChannelMembers(
  hubId: number,
  channelId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  const PARTICIPANTS_FETCH_SIZE = 100; // Match the size used in MentionInput

  // Fetch channel details to determine scope and get custom channel participants
  const channelQuery = $api.useQuery('get', '/api/hubs/{hubId}/chats/{channelId}', {
    params: {
      path: { hubId, channelId },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId && !!channelId,
    // Cache data for 1 minute (participant list changes less frequently)
    staleTime: options?.staleTime ?? 1 * 60 * 1000,
    // Enable automatic refetching on window focus for fresh status
    refetchOnWindowFocus: true,
  });

  // Fetch all hub participants as fallback for general channels
  const { data: hubParticipantsData, isLoading: isLoadingHubParticipants } = useHubParticipants(hubId, {
    enabled: options?.enabled !== false && !!hubId,
    size: PARTICIPANTS_FETCH_SIZE
  });

  // Determine the correct participant data source based on channel scope
  const transformedData = useMemo(() => {
    if (!channelQuery.data) {
      return undefined;
    }

    // Check if this is a general channel (scope = 'general') or if participants array is empty/null
    // For general channels, backend returns participants = null, so we should use all hub participants
    const isGeneralChannel = channelQuery.data.scope === ChatChannelResponseScope.general ||
                             !channelQuery.data.participants ||
                             channelQuery.data.participants.length === 0;

    if (isGeneralChannel) {
      // General channel: use all hub participants
      const hubParticipants = hubParticipantsData?.content || [];
      return {
        content: hubParticipants,
        totalElements: hubParticipantsData?.totalElements || 0,
        totalPages: hubParticipantsData?.totalPages || 1,
        size: hubParticipants.length,
        number: 0,
        numberOfElements: hubParticipants.length,
        first: true,
        last: true,
        empty: hubParticipants.length === 0
      };
    } else {
      // Custom channel: use the specific channel participants
      const channelParticipants = (channelQuery.data.participants || []).map((participant): HubParticipantResponse => ({
        id: participant.id!,
        userId: undefined, // ChatParticipantDto doesn't include userId
        email: participant.email || '',
        name: participant.name || '',
        role: participant.role as any, // Role enums should be compatible
        isExternal: participant.is_external || false,
        invitedAt: new Date().toISOString(), // Default value since ChatParticipantDto doesn't include this
        joinedAt: new Date().toISOString(), // Default value since ChatParticipantDto doesn't include this
        status: 'active' // Default to active since they're channel members
      }));

      return {
        content: channelParticipants,
        totalElements: channelQuery.data.participant_count || 0,
        totalPages: 1,
        size: channelParticipants.length,
        number: 0,
        numberOfElements: channelParticipants.length,
        first: true,
        last: true,
        empty: channelParticipants.length === 0
      };
    }
  }, [channelQuery.data, hubParticipantsData]);

  // Unified loading state - loading if either query is loading
  const isLoading = channelQuery.isLoading || (channelQuery.data?.scope === ChatChannelResponseScope.general && isLoadingHubParticipants);

  return {
    ...channelQuery,
    data: transformedData,
    isLoading
  };
}
