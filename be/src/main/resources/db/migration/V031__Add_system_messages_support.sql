-- Migration V031: Add system messages support to chat system
-- This migration adds message_type enum to distinguish between user messages and system messages

-- 1. Create message_type enum
CREATE TYPE message_type AS ENUM ('USER', 'MEMBER_ADDED', 'MEMBER_REMOVED');

-- 2. Add message_type column to chat_message table
ALTER TABLE chat_message 
ADD COLUMN message_type message_type NOT NULL DEFAULT 'USER';

-- 3. Create index on message_type for efficient filtering
CREATE INDEX idx_chat_message_type ON chat_message(message_type);

-- 4. Create composite index for channel_id and message_type for efficient queries
CREATE INDEX idx_chat_message_channel_type ON chat_message(channel_id, message_type);

-- 5. Add comment to document the purpose
COMMENT ON COLUMN chat_message.message_type IS 'Type of message: USER for regular messages, MEMBER_ADDED/MEMBER_REMOVED for system messages';
