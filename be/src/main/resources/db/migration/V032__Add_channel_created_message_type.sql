-- Migration V032: Add CHANNEL_CREATED message type for system messages
-- This migration adds CHANNEL_CREATED to the message_type enum to support channel creation system messages

-- Add CHANNEL_CREATED to the message_type enum
ALTER TYPE message_type ADD VALUE 'CHANNEL_CREATED';

-- Update the comment to document the new message type
COMMENT ON COLUMN chat_message.message_type IS 'Type of message: USER for regular messages, MEMBER_ADDED/MEMBER_REMOVED/CHANNEL_CREATED for system messages';
