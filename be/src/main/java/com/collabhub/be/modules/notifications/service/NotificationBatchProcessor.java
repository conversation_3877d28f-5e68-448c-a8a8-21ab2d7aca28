package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.config.NotificationBatchingProperties;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.notifications.repository.NotificationBatchProcessingLockRepository;
import com.collabhub.be.modules.notifications.repository.NotificationBatchQueueRepository;
import org.jooq.generated.tables.pojos.NotificationBatchQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.InetAddress;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Scheduled processor for handling batched email notifications.
 * Runs periodically to process accumulated notifications and send consolidated emails.
 */
@Service
@Transactional
public class NotificationBatchProcessor {

    private static final Logger logger = LoggerFactory.getLogger(NotificationBatchProcessor.class);
    
    private static final String BATCH_PROCESSOR_LOCK_KEY = "notification_batch_processor";
    private static final int CLEANUP_BATCH_SIZE = 1000;

    private final NotificationBatchingProperties batchingProperties;
    private final NotificationBatchQueueRepository batchQueueRepository;
    private final NotificationBatchProcessingLockRepository lockRepository;
    private final BatchedEmailNotificationService batchedEmailService;
    private final NotificationBatchProcessor self;
    private final String instanceId;

    public NotificationBatchProcessor(NotificationBatchingProperties batchingProperties,
                                      NotificationBatchQueueRepository batchQueueRepository,
                                      NotificationBatchProcessingLockRepository lockRepository,
                                      BatchedEmailNotificationService batchedEmailService, @Lazy NotificationBatchProcessor self) {
        this.batchingProperties = batchingProperties;
        this.batchQueueRepository = batchQueueRepository;
        this.lockRepository = lockRepository;
        this.batchedEmailService = batchedEmailService;
        this.self = self;
        this.instanceId = generateInstanceId();
    }

    /**
     * Main scheduled method that processes batched notifications.
     * Runs at the configured interval to send accumulated notifications.
     */
    @Scheduled(fixedRateString = "#{@notificationBatchingProperties.processIntervalSeconds * 1000}")
    public void processBatchedNotifications() {
        if (!batchingProperties.isEnabled()) {
            logger.debug("Notification batching is disabled, skipping batch processing");
            return;
        }

        logger.debug("Starting batch notification processing");

        // Try to acquire distributed lock
        boolean lockAcquired = lockRepository.acquireLock(
            BATCH_PROCESSOR_LOCK_KEY, 
            instanceId, 
            batchingProperties.getLockTimeoutMinutes()
        );

        if (!lockAcquired) {
            logger.debug("Could not acquire batch processing lock, another instance is processing");
            return;
        }

        try {
            self.processReadyBatches();
            self.processRetryableNotifications();
        } catch (Exception e) {
            logger.error("Error during batch notification processing", e);
        } finally {
            // Always release the lock
            lockRepository.releaseLock(BATCH_PROCESSOR_LOCK_KEY, instanceId);
        }
    }

    /**
     * Processes batches that are ready for delivery.
     * All unsent notifications are processed regardless of urgency or creation time.
     */
    @Transactional
    protected void processReadyBatches() {
        logger.debug("Processing all ready batches");

        // Get all pending notifications grouped by recipient (no time-based considerations)
        Map<String, List<NotificationBatchQueue>> readyBatches =
            batchQueueRepository.findAllPendingNotifications();

        if (readyBatches.isEmpty()) {
            logger.debug("No ready batches found for processing");
            return;
        }

        logger.info("Processing {} ready batches", readyBatches.size());

        for (Map.Entry<String, List<NotificationBatchQueue>> entry : readyBatches.entrySet()) {
            String batchKey = entry.getKey();
            List<NotificationBatchQueue> notifications = entry.getValue();

            try {
                processSingleBatch(batchKey, notifications);
            } catch (Exception e) {
                logger.error("Failed to process batch: {}", batchKey, e);
                handleBatchProcessingFailure(notifications, e.getMessage());
            }
        }
    }



    /**
     * Processes a single batch of notifications for a recipient (internal user or external participant).
     */
    private void processSingleBatch(String batchKey, List<NotificationBatchQueue> notifications) {
        if (notifications.isEmpty()) {
            return;
        }

        // Determine recipient type and identifier from the first notification
        NotificationBatchQueue firstNotification = notifications.getFirst();
        String recipientKey = generateRecipientKey(firstNotification);
        String recipientIdentifier = extractRecipientIdentifier(recipientKey);
        boolean isInternal = isInternalUser(recipientKey);

        // Log detailed batch information for debugging
        String notificationTypes = notifications.stream()
                .map(n -> n.getNotificationType().name())
                .distinct()
                .collect(Collectors.joining(", "));
        String urgencyLevels = notifications.stream()
                .map(n -> n.getUrgency().name())
                .distinct()
                .collect(Collectors.joining(", "));
        String batchWindows = notifications.stream()
                .map(n -> n.getBatchWindowStart().toString())
                .distinct()
                .collect(Collectors.joining(", "));

        logger.info("BATCH PROCESSING START: batch_key={}, recipient_type={}, recipient={}, notification_count={}, types=[{}], urgencies=[{}], batch_windows=[{}]",
                   batchKey, isInternal ? "internal_user" : "external_participant", recipientIdentifier,
                   notifications.size(), notificationTypes, urgencyLevels, batchWindows);

        // Mark notifications as processing
        List<Long> notificationIds = notifications.stream()
                .map(NotificationBatchQueue::getId)
                .collect(Collectors.toList());

        int markedCount = batchQueueRepository.markAsProcessing(notificationIds);
        if (markedCount != notifications.size()) {
            logger.warn("Expected to mark {} notifications as processing, but marked {}",
                       notifications.size(), markedCount);
        }

        try {
            boolean emailSent;

            if (isInternal) {
                // Send batched email to internal user
                Long userId = Long.parseLong(recipientIdentifier);
                emailSent = batchedEmailService.sendBatchedEmail(userId, notifications);
            } else {
                // Send batched email to external participant
                String email = recipientIdentifier;
                emailSent = batchedEmailService.sendBatchedEmailToExternal(email, notifications);
            }

            if (emailSent) {
                // Mark as successfully sent
                batchQueueRepository.markAsSent(notificationIds);
                logger.info("BATCHED EMAIL SENT: Successfully sent batched email to {} {} with {} notifications (types: {})",
                           isInternal ? "user" : "external participant", recipientIdentifier, notifications.size(),
                           notifications.stream().map(n -> n.getNotificationType().name()).distinct().collect(java.util.stream.Collectors.joining(", ")));
            } else {
                // Mark as failed for retry
                batchQueueRepository.markAsFailed(notificationIds, "Email delivery failed");
                logger.warn("Failed to send batched email to {} {}",
                           isInternal ? "user" : "external participant", recipientIdentifier);
            }

        } catch (Exception e) {
            // Mark as failed for retry
            batchQueueRepository.markAsFailed(notificationIds, e.getMessage());
            logger.error("Exception while sending batched email to {} {}",
                        isInternal ? "user" : "external participant", recipientIdentifier, e);
        }
    }

    /**
     * Processes notifications that are ready for retry.
     */
    @Transactional
    protected void processRetryableNotifications() {
        LocalDateTime retryAfter = LocalDateTime.now().minusMinutes(batchingProperties.getRetryDelayMinutes());

        List<NotificationBatchQueue> retryableNotifications =
            batchQueueRepository.findRetryableNotifications(retryAfter, batchingProperties.getMaxRetryAttempts());

        if (retryableNotifications.isEmpty()) {
            logger.debug("No notifications ready for retry");
            return;
        }

        logger.info("Processing {} notifications for retry", retryableNotifications.size());

        // Group by recipient (supports both internal users and external participants)
        Map<String, List<NotificationBatchQueue>> notificationsByRecipient = retryableNotifications.stream()
                .collect(Collectors.groupingBy(this::generateRecipientKey));

        for (Map.Entry<String, List<NotificationBatchQueue>> entry : notificationsByRecipient.entrySet()) {
            String recipientKey = entry.getKey();
            List<NotificationBatchQueue> recipientNotifications = entry.getValue();

            try {
                // Reset to pending and let normal processing handle them
                List<Long> notificationIds = recipientNotifications.stream()
                        .map(NotificationBatchQueue::getId)
                        .collect(Collectors.toList());

                batchQueueRepository.resetToPending(notificationIds);
                logger.debug("Reset {} notifications to pending for recipient {}", recipientNotifications.size(), recipientKey);

            } catch (Exception e) {
                logger.error("Failed to reset notifications for retry for recipient {}", recipientKey, e);
            }
        }
    }

    /**
     * Handles batch processing failures by marking notifications as failed.
     */
    private void handleBatchProcessingFailure(List<NotificationBatchQueue> notifications, String errorMessage) {
        List<Long> notificationIds = notifications.stream()
                .map(NotificationBatchQueue::getId)
                .collect(Collectors.toList());
        
        batchQueueRepository.markAsFailed(notificationIds, errorMessage);
    }

    /**
     * Scheduled cleanup of old processed notifications.
     */
    @Scheduled(cron = "0 0 2 * * *") // Daily at 2 AM
    public void cleanupProcessedNotifications() {
        if (!batchingProperties.isEnabled()) {
            return;
        }

        logger.debug("Starting cleanup of processed notifications");

        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(batchingProperties.getCleanupRetentionHours());
        int deletedCount = batchQueueRepository.cleanupProcessedNotifications(cutoffTime, CLEANUP_BATCH_SIZE);
        
        logger.info("Cleaned up {} processed notifications", deletedCount);

        // Also cleanup expired locks
        int expiredLocks = lockRepository.cleanupExpiredLocks();
        if (expiredLocks > 0) {
            logger.info("Cleaned up {} expired processing locks", expiredLocks);
        }
    }

    /**
     * Processes any pending batches on application startup.
     */
    @EventListener(ApplicationReadyEvent.class)
    public void processPendingBatchesOnStartup() {
        if (!batchingProperties.isEnabled()) {
            return;
        }

        logger.info("Processing pending batches on application startup");
        
        try {
            processBatchedNotifications();
        } catch (Exception e) {
            logger.error("Error processing pending batches on startup", e);
        }
    }

    /**
     * Generates a unique recipient key for grouping notifications.
     *
     * <p>This method creates a consistent identifier for both internal users (user_id)
     * and external participants (email), enabling unified batch processing.</p>
     *
     * @param notification the notification to generate a key for
     * @return recipient key in format "user:{userId}" or "email:{email}"
     */
    private String generateRecipientKey(NotificationBatchQueue notification) {
        if (notification.getUserId() != null) {
            return "user:" + notification.getUserId();
        } else if (notification.getEmail() != null) {
            return "email:" + notification.getEmail().toLowerCase().trim();
        } else {
            throw new IllegalStateException("Notification must have either userId or email: " + notification.getId());
        }
    }

    /**
     * Extracts the recipient identifier from a recipient key.
     *
     * @param recipientKey the recipient key (format: "user:{userId}" or "email:{email}")
     * @return the identifier part (userId as string or email)
     */
    private String extractRecipientIdentifier(String recipientKey) {
        if (recipientKey.startsWith("user:")) {
            return recipientKey.substring(5); // Remove "user:" prefix
        } else if (recipientKey.startsWith("email:")) {
            return recipientKey.substring(6); // Remove "email:" prefix
        } else {
            throw new IllegalArgumentException("Invalid recipient key format: " + recipientKey);
        }
    }

    /**
     * Checks if a recipient key represents an internal user.
     *
     * @param recipientKey the recipient key
     * @return true if internal user, false if external participant
     */
    private boolean isInternalUser(String recipientKey) {
        return recipientKey.startsWith("user:");
    }

    /**
     * Generates a unique instance identifier for distributed locking.
     */
    private String generateInstanceId() {
        try {
            String hostname = InetAddress.getLocalHost().getHostName();
            return hostname + "-" + System.currentTimeMillis();
        } catch (Exception e) {
            return "unknown-" + System.currentTimeMillis();
        }
    }
}
