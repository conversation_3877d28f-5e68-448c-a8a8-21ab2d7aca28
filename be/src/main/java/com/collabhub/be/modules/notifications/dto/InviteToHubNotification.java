package com.collabhub.be.modules.notifications.dto;

import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Strongly-typed notification for collaboration hub invitations.
 * 
 * <p>This notification is sent when a user is invited to join a collaboration hub.
 * It provides type-safe properties for all invitation-related data and generates
 * appropriate invitation messages with deep links.</p>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * InviteToHubNotification notification = InviteToHubNotification.builder()
 *     .recipients(invitedUsers)
 *     .inviterName("Jane Smith")
 *     .hubTitle("Summer Campaign 2024")
 *     .invitationRole("Content Creator")
 *     .hubId(123L)
 *     .build();
 * </pre>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public class InviteToHubNotification extends BaseNotification {

    // ========================================
    // TYPE-SPECIFIC PROPERTIES
    // ========================================

    /**
     * The name of the user who sent the invitation.
     */
    @NotBlank(message = "Inviter name cannot be blank")
    @Size(max = 255, message = "Inviter name cannot exceed 255 characters")
    private final String inviterName;

    /**
     * The title of the collaboration hub being invited to.
     */
    @NotBlank(message = "Hub title cannot be blank")
    @Size(max = 500, message = "Hub title cannot exceed 500 characters")
    private final String hubTitle;

    /**
     * The role the invitee will have in the hub (e.g., "Content Creator", "Reviewer").
     */
    @NotBlank(message = "Invitation role cannot be blank")
    @Size(max = 100, message = "Invitation role cannot exceed 100 characters")
    private final String invitationRole;

    /**
     * Optional personal message from the inviter.
     */
    @Size(max = 1000, message = "Personal message cannot exceed 1000 characters")
    private final String personalMessage;

    /**
     * The ID of the collaboration hub being invited to.
     */
    @NotNull(message = "Hub ID cannot be null")
    private final Long hubId;

    // ========================================
    // CONSTRUCTOR
    // ========================================

    /**
     * Private constructor for builder pattern.
     */
    private InviteToHubNotification(Builder builder) {
        super(builder.recipients, builder.urgency, builder.entityReferences, builder.locale);
        this.inviterName = builder.inviterName;
        this.hubTitle = builder.hubTitle;
        this.invitationRole = builder.invitationRole;
        this.personalMessage = builder.personalMessage;
        this.hubId = builder.hubId;
    }

    // ========================================
    // ABSTRACT METHOD IMPLEMENTATIONS
    // ========================================

    @Override
    public NotificationType getType() {
        return NotificationType.INVITE_TO_HUB;
    }

    @Override
    protected Map<String, Object> buildParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("inviterName", inviterName);
        parameters.put("hubTitle", hubTitle);
        parameters.put("invitationRole", invitationRole);
        if (personalMessage != null && !personalMessage.trim().isEmpty()) {
            parameters.put("personalMessage", personalMessage.trim());
        }
        return parameters;
    }

    @Override
    public NotificationMetadata generateMetadata() {
        NotificationMetadata.Builder builder = NotificationMetadata.builder()
                .actorName(inviterName)
                .targetTitle(hubTitle)
                .actionContext("invited you to join a collaboration hub")
                .invitationRole(invitationRole)
                .deepLinkPath(String.format("/app/hubs/%d", hubId));

        if (personalMessage != null && !personalMessage.trim().isEmpty()) {
            builder.commentPreview(personalMessage.trim());
        }

        return builder.build();
    }

    // ========================================
    // VALIDATION
    // ========================================

    @Override
    protected void validateSpecific() {
        if (hubId == null || hubId <= 0) {
            throw new IllegalArgumentException("Hub ID must be a positive number");
        }
    }

    // ========================================
    // GETTERS
    // ========================================

    public String getInviterName() {
        return inviterName;
    }

    public String getHubTitle() {
        return hubTitle;
    }

    public String getInvitationRole() {
        return invitationRole;
    }

    public String getPersonalMessage() {
        return personalMessage;
    }

    public Long getHubId() {
        return hubId;
    }

    // ========================================
    // BUILDER PATTERN
    // ========================================

    /**
     * Creates a new builder for InviteToHubNotification.
     * 
     * @return a new builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for InviteToHubNotification.
     */
    public static class Builder {
        private List<NotificationRecipient> recipients;
        private NotificationUrgency urgency = NotificationUrgency.HIGH; // Default for invitations
        private NotificationStorageService.EntityReferences entityReferences;
        private Locale locale;
        private String inviterName;
        private String hubTitle;
        private String invitationRole;
        private String personalMessage;
        private Long hubId;

        private Builder() {}

        public Builder recipients(@NotEmpty List<NotificationRecipient> recipients) {
            this.recipients = recipients;
            return this;
        }

        public Builder urgency(@NotNull NotificationUrgency urgency) {
            this.urgency = urgency;
            return this;
        }

        public Builder entityReferences(NotificationStorageService.EntityReferences entityReferences) {
            this.entityReferences = entityReferences;
            return this;
        }

        public Builder locale(Locale locale) {
            this.locale = locale;
            return this;
        }

        public Builder inviterName(@NotBlank String inviterName) {
            this.inviterName = inviterName;
            return this;
        }

        public Builder hubTitle(@NotBlank String hubTitle) {
            this.hubTitle = hubTitle;
            return this;
        }

        public Builder invitationRole(@NotBlank String invitationRole) {
            this.invitationRole = invitationRole;
            return this;
        }

        public Builder personalMessage(String personalMessage) {
            this.personalMessage = personalMessage;
            return this;
        }

        public Builder hubId(@NotNull Long hubId) {
            this.hubId = hubId;
            return this;
        }

        /**
         * Convenience method to set entity references from hub ID.
         */
        public Builder entityIds(Long hubId) {
            this.hubId = hubId;
            this.entityReferences = NotificationStorageService.EntityReferences.hub(hubId);
            return this;
        }

        /**
         * Builds the InviteToHubNotification with validation.
         * 
         * @return a new InviteToHubNotification instance
         * @throws IllegalArgumentException if required fields are missing or invalid
         */
        public InviteToHubNotification build() {
            // Set entity references if not already set
            if (entityReferences == null && hubId != null) {
                entityReferences = NotificationStorageService.EntityReferences.hub(hubId);
            }

            InviteToHubNotification notification = new InviteToHubNotification(this);
            notification.validate();
            return notification;
        }
    }
}
