package com.collabhub.be.modules.chat.repository;

import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.generated.enums.MessageType;
import org.jooq.generated.tables.daos.ChatMessageDao;
import org.jooq.generated.tables.pojos.ChatMessage;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.jooq.generated.tables.ChatMessage.CHAT_MESSAGE;

/**
 * Repository for ChatMessage entity using jOOQ for database operations.
 * Provides type-safe database operations for chat message management.
 */
@Repository
public class ChatMessageRepositoryImpl extends ChatMessageDao {

    private final DSLContext dsl;

    public ChatMessageRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds messages in a channel with pagination (for infinite scroll).
     * Messages are ordered by creation time descending (newest first).
     * 
     * @param channelId the channel ID
     * @param limit the maximum number of messages to return
     * @param beforeMessageId optional message ID to get messages before (for pagination)
     * @return list of messages
     */
    public List<ChatMessage> findMessagesByChannelWithPagination(Long channelId, int limit, Long beforeMessageId) {
        var query = dsl.select()
                .from(CHAT_MESSAGE)
                .where(CHAT_MESSAGE.CHANNEL_ID.eq(channelId));

        // Add pagination condition if beforeMessageId is provided
        if (beforeMessageId != null) {
            // Get the creation time of the reference message
            LocalDateTime beforeTime = dsl.select(CHAT_MESSAGE.CREATED_AT)
                    .from(CHAT_MESSAGE)
                    .where(CHAT_MESSAGE.ID.eq(beforeMessageId))
                    .fetchOne(CHAT_MESSAGE.CREATED_AT);
            
            if (beforeTime != null) {
                query = query.and(CHAT_MESSAGE.CREATED_AT.lt(beforeTime));
            }
        }

        return query.orderBy(CHAT_MESSAGE.CREATED_AT.desc())
                .limit(limit)
                .fetchInto(ChatMessage.class);
    }

    /**
     * Finds the latest message in a channel.
     * 
     * @param channelId the channel ID
     * @return optional latest message
     */
    public Optional<ChatMessage> findLatestMessageByChannel(Long channelId) {
        return dsl.select()
                .from(CHAT_MESSAGE)
                .where(CHAT_MESSAGE.CHANNEL_ID.eq(channelId))
                .orderBy(CHAT_MESSAGE.CREATED_AT.desc())
                .limit(1)
                .fetchOptionalInto(ChatMessage.class);
    }

    /**
     * Counts total messages in a channel.
     * 
     * @param channelId the channel ID
     * @return message count
     */
    public Long countMessagesByChannel(Long channelId) {
        return dsl.selectCount()
                .from(CHAT_MESSAGE)
                .where(CHAT_MESSAGE.CHANNEL_ID.eq(channelId))
                .fetchOne(0, Long.class);
    }

    /**
     * Creates a new chat message with mentions.
     * Note: Attachments are now handled via junction tables, not stored in message entity.
     *
     * @param channelId the channel ID
     * @param participantId the sender participant ID
     * @param content the message content
     * @param mentions JSONB array of mentions
     * @return the created message
     */
    public ChatMessage createMessage(Long channelId, Long participantId, String content,
                                   JSONB mentions) {
        LocalDateTime now = LocalDateTime.now();

        ChatMessage message = new ChatMessage();
        message.setChannelId(channelId);
        message.setParticipantId(participantId);
        message.setContent(content);
        message.setMentions(mentions);
        // Note: attachment_uris no longer used - attachments handled via chat_message_media junction table
        message.setCreatedAt(now);

        insert(message);
        return message;
    }

    /**
     * Creates a new system message.
     * System messages are automatically generated for events like member additions/removals.
     *
     * @param channelId the channel ID
     * @param participantId the participant who performed the action
     * @param content the system message content
     * @param messageType the type of system message
     * @param mentions JSONB array of mentions (usually empty for system messages)
     * @return the created system message
     */
    public ChatMessage createSystemMessage(Long channelId, Long participantId, String content,
                                           MessageType messageType, JSONB mentions) {
        LocalDateTime now = LocalDateTime.now();

        ChatMessage message = new ChatMessage();
        message.setChannelId(channelId);
        message.setParticipantId(participantId);
        message.setContent(content);
        message.setMentions(mentions);
        message.setMessageType(messageType);
        message.setCreatedAt(now);

        insert(message);
        return message;
    }

    /**
     * Updates a chat message content and attachments.
     * Sets updated_at and edited_at timestamps in application layer.
     * 
     * @param messageId the message ID
     * @param content the new content
     * @param mentions JSONB array of mentions
     * @return true if message was updated, false if not found
     */
    public boolean updateMessage(Long messageId, String content, JSONB mentions) {
        LocalDateTime now = LocalDateTime.now();
        
        int updatedRows = dsl.update(CHAT_MESSAGE)
                .set(CHAT_MESSAGE.CONTENT, content)
                .set(CHAT_MESSAGE.MENTIONS, mentions)
                // Note: Attachments are now managed through chat_message_media junction table
                .set(CHAT_MESSAGE.UPDATED_AT, now)
                .set(CHAT_MESSAGE.EDITED_AT, now)
                .where(CHAT_MESSAGE.ID.eq(messageId))
                .execute();
        
        return updatedRows > 0;
    }

    /**
     * Deletes a chat message by ID.
     * 
     * @param messageId the message ID
     * @return true if message was deleted, false if not found
     */
    public boolean deleteMessage(Long messageId) {
        int deletedRows = dsl.deleteFrom(CHAT_MESSAGE)
                .where(CHAT_MESSAGE.ID.eq(messageId))
                .execute();
        
        return deletedRows > 0;
    }

    /**
     * Finds a message by ID and verifies it belongs to the specified channel.
     * 
     * @param messageId the message ID
     * @param channelId the channel ID
     * @return optional message if found and belongs to channel
     */
    public Optional<ChatMessage> findMessageByIdAndChannel(Long messageId, Long channelId) {
        return dsl.select()
                .from(CHAT_MESSAGE)
                .where(CHAT_MESSAGE.ID.eq(messageId))
                .and(CHAT_MESSAGE.CHANNEL_ID.eq(channelId))
                .fetchOptionalInto(ChatMessage.class);
    }

}
