package com.collabhub.be.modules.notifications.dto;

import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.service.NotificationTranslationService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Production-grade strongly-typed notification for chat channel additions.
 *
 * <p>This notification is sent when a user is added to a chat channel.
 * It provides type-safe handling of chat addition context including the adder,
 * channel information, and invitation details.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Immutable value object with comprehensive validation</li>
 *   <li>Builder pattern for flexible construction</li>
 *   <li>Automatic integration with NotificationTranslationService</li>
 *   <li>Entity references for deep linking to chat channels</li>
 * </ul>
 *
 * <h3>Usage Example:</h3>
 * <pre>
 * ChatAddedNotification notification = ChatAddedNotification.builder()
 *     .recipients(recipients)
 *     .adderName("John Doe")
 *     .channelName("Project Alpha Discussion")
 *     .entityIds(hubId, channelId)
 *     .locale(Locale.ENGLISH)
 *     .build();
 *
 * notificationDispatcherService.dispatch(notification);
 * </pre>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public final class ChatAddedNotification extends BaseNotification {

    @NotBlank
    @Size(max = 100)
    private final String adderName;

    @NotBlank
    @Size(max = 255)
    private final String channelName;

    /**
     * Private constructor for immutable object creation.
     */
    private ChatAddedNotification(Builder builder) {
        super(
            builder.recipients,
            builder.urgency,
            builder.entityReferences,
            builder.locale
        );
        this.adderName = builder.adderName;
        this.channelName = builder.channelName;
    }

    /**
     * Creates a new builder for ChatAddedNotification.
     *
     * @return new Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    @Override
    public NotificationType getType() {
        return NotificationType.CHAT_ADDED;
    }

    @Override
    protected Map<String, Object> buildParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("adderName", adderName);
        parameters.put("channelName", channelName);
        return parameters;
    }

    @Override
    protected void validateSpecific() {
        if (adderName == null || adderName.trim().isEmpty()) {
            throw new IllegalArgumentException("Adder name cannot be null or empty");
        }
        if (channelName == null || channelName.trim().isEmpty()) {
            throw new IllegalArgumentException("Channel name cannot be null or empty");
        }
        if (adderName.length() > 100) {
            throw new IllegalArgumentException("Adder name exceeds maximum length of 100 characters");
        }
        if (channelName.length() > 255) {
            throw new IllegalArgumentException("Channel name exceeds maximum length of 255 characters");
        }
    }

    // Getters
    public String getAdderName() {
        return adderName;
    }

    public String getChannelName() {
        return channelName;
    }

    @Override
    public String toString() {
        return String.format("ChatAddedNotification{type=%s, adderName='%s', channelName='%s', recipients=%d}",
                           getType(), adderName, channelName, getRecipients().size());
    }

    /**
     * Builder class for flexible ChatAddedNotification construction.
     */
    public static final class Builder {
        private List<NotificationRecipient> recipients;
        private String adderName;
        private String channelName;
        private Long hubId;
        private Long channelId;
        private NotificationStorageService.EntityReferences entityReferences;
        private NotificationMetadata metadata;
        private NotificationUrgency urgency = NotificationUrgency.NORMAL;
        private Locale locale = Locale.ENGLISH;

        private Builder() {}

        public Builder recipients(@NotEmpty @Valid List<NotificationRecipient> recipients) {
            this.recipients = recipients;
            return this;
        }

        public Builder adderName(@NotBlank String adderName) {
            this.adderName = adderName;
            return this;
        }

        public Builder channelName(@NotBlank String channelName) {
            this.channelName = channelName;
            return this;
        }

        public Builder hubId(@NotNull Long hubId) {
            this.hubId = hubId;
            return this;
        }

        public Builder channelId(@NotNull Long channelId) {
            this.channelId = channelId;
            return this;
        }

        public Builder entityReferences(NotificationStorageService.EntityReferences entityReferences) {
            this.entityReferences = entityReferences;
            return this;
        }

        public Builder metadata(@Valid NotificationMetadata metadata) {
            this.metadata = metadata;
            return this;
        }

        public Builder urgency(@NotNull NotificationUrgency urgency) {
            this.urgency = urgency;
            return this;
        }

        public Builder locale(@NotNull Locale locale) {
            this.locale = locale;
            return this;
        }

        /**
         * Convenience method to set entity references from individual IDs.
         */
        public Builder entityIds(Long hubId, Long channelId) {
            this.hubId = hubId;
            this.channelId = channelId;
            this.entityReferences = NotificationStorageService.EntityReferences.chat(hubId, channelId);
            return this;
        }

        /**
         * Builds the ChatAddedNotification with validation.
         * 
         * @return a new ChatAddedNotification instance
         * @throws IllegalArgumentException if required fields are missing or invalid
         */
        public ChatAddedNotification build() {
            // Set entity references if not already set
            if (entityReferences == null && hubId != null && channelId != null) {
                entityReferences = NotificationStorageService.EntityReferences.chat(hubId, channelId);
            }

            ChatAddedNotification notification = new ChatAddedNotification(this);
            notification.validate();
            return notification;
        }
    }
}
