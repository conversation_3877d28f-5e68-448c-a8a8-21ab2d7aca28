package com.collabhub.be.modules.notifications.dto;

import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.service.NotificationTranslationService;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Abstract base class for all strongly-typed notification objects.
 * 
 * <p>This class provides a type-safe foundation for notification creation, eliminating
 * the need for generic HashMaps and manual title/message construction. Each concrete
 * notification class extends this base to provide type-specific properties and
 * automatic title/message generation.</p>
 * 
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Strongly-typed properties with compile-time validation</li>
 *   <li>Automatic title/message generation with i18n support</li>
 *   <li>Integration with existing notification infrastructure</li>
 *   <li>Support for both internal and external recipients</li>
 *   <li>Proper validation annotations and error handling</li>
 * </ul>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * CommentMentionNotification notification = CommentMentionNotification.builder()
 *     .recipients(mentionedParticipants)
 *     .commenterName("John Doe")
 *     .postTitle("Summer Campaign")
 *     .commentPreview("Great work on this...")
 *     .hubId(123L)
 *     .postId(456L)
 *     .commentId(789L)
 *     .urgency(NotificationUrgency.HIGH)
 *     .build();
 * 
 * notificationDispatcher.dispatch(notification);
 * </pre>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public abstract class BaseNotification {

    private static final Logger logger = LoggerFactory.getLogger(BaseNotification.class);

    // ========================================
    // COMMON PROPERTIES
    // ========================================

    /**
     * The recipients who should receive this notification.
     * Can include both internal users and external participants.
     */
    @NotEmpty(message = "Recipients list cannot be empty")
    @Valid
    protected final List<NotificationRecipient> recipients;

    /**
     * The urgency level of this notification.
     * Determines processing priority and delivery timing.
     */
    @NotNull(message = "Notification urgency cannot be null")
    protected final NotificationUrgency urgency;

    /**
     * Optional entity references for deep linking and context.
     * Used to generate proper navigation URLs in notifications.
     */
    protected final NotificationStorageService.EntityReferences entityReferences;

    /**
     * Optional locale for internationalization.
     * If null, the system default locale will be used.
     */
    protected final Locale locale;

    // ========================================
    // CONSTRUCTOR
    // ========================================

    /**
     * Protected constructor for use by concrete notification classes.
     * 
     * @param recipients the notification recipients (must not be empty)
     * @param urgency the notification urgency (must not be null)
     * @param entityReferences optional entity references for deep linking
     * @param locale optional locale for i18n (null uses system default)
     */
    protected BaseNotification(@NotEmpty List<NotificationRecipient> recipients,
                              @NotNull NotificationUrgency urgency,
                              NotificationStorageService.EntityReferences entityReferences,
                              Locale locale) {
        this.recipients = List.copyOf(recipients); // Immutable copy
        this.urgency = urgency;
        this.entityReferences = entityReferences;
        this.locale = locale != null ? locale : Locale.forLanguageTag("english");
    }

    // ========================================
    // ABSTRACT METHODS
    // ========================================

    /**
     * Returns the notification type for this notification.
     * Each concrete class must specify its type.
     * 
     * @return the notification type
     */
    public abstract NotificationType getType();

    /**
     * Builds the parameters map for title/message generation.
     * Each concrete class provides its specific parameters.
     * 
     * @return map of parameters for template substitution
     */
    protected abstract Map<String, Object> buildParameters();

    // ========================================
    // PUBLIC METHODS
    // ========================================

    /**
     * Generates the localized notification title using the translation service.
     * 
     * @param translationService the translation service for i18n
     * @return the localized notification title
     */
    public String generateTitle(NotificationTranslationService translationService) {
        try {
            return translationService.getNotificationTitle(getType(), locale);
        } catch (Exception e) {
            logger.warn("Failed to generate title for notification type {}: {}", getType(), e.getMessage());
            return getType().name().replace("_", " ");
        }
    }

    /**
     * Generates the localized notification message using the translation service.
     * 
     * @param translationService the translation service for i18n
     * @return the localized notification message with parameter substitution
     */
    public String generateMessage(NotificationTranslationService translationService) {
        try {
            Map<String, Object> parameters = buildParameters();
            return translationService.getNotificationMessage(getType(), locale, parameters);
        } catch (Exception e) {
            logger.warn("Failed to generate message for notification type {}: {}", getType(), e.getMessage());
            return "You have a new " + getType().name().toLowerCase().replace("_", " ") + " notification.";
        }
    }

    /**
     * Generates the notification metadata for this notification.
     * Default implementation creates basic metadata; subclasses can override.
     * 
     * @return the notification metadata
     */
    public NotificationMetadata generateMetadata() {
        return NotificationMetadata.builder()
                .build(); // Basic metadata; subclasses can override
    }

    // ========================================
    // GETTERS
    // ========================================

    /**
     * Gets the notification recipients.
     * 
     * @return immutable list of recipients
     */
    public List<NotificationRecipient> getRecipients() {
        return recipients;
    }

    /**
     * Gets the notification urgency.
     * 
     * @return the urgency level
     */
    public NotificationUrgency getUrgency() {
        return urgency;
    }

    /**
     * Gets the entity references for deep linking.
     * 
     * @return the entity references, or null if not set
     */
    public NotificationStorageService.EntityReferences getEntityReferences() {
        return entityReferences;
    }

    /**
     * Gets the locale for internationalization.
     * 
     * @return the locale (never null)
     */
    public Locale getLocale() {
        return locale;
    }

    // ========================================
    // VALIDATION METHODS
    // ========================================

    /**
     * Validates this notification object.
     * Can be overridden by subclasses for additional validation.
     * 
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (recipients == null || recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipients list cannot be null or empty");
        }
        
        if (urgency == null) {
            throw new IllegalArgumentException("Notification urgency cannot be null");
        }

        // Additional validation can be added by subclasses
        validateSpecific();
    }

    /**
     * Template method for subclass-specific validation.
     * Override this method to add type-specific validation logic.
     */
    protected void validateSpecific() {
        // Default implementation does nothing
    }

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Returns a string representation of this notification for debugging.
     * 
     * @return string representation
     */
    @Override
    public String toString() {
        return String.format("%s{type=%s, recipients=%d, urgency=%s}",
                getClass().getSimpleName(), getType(), recipients.size(), urgency);
    }
}
