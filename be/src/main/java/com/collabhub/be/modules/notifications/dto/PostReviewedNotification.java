package com.collabhub.be.modules.notifications.dto;

import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Strongly-typed notification for post review completions.
 * 
 * <p>This notification is sent when a reviewer completes their review of a post.
 * It provides type-safe properties for all review-related data and generates
 * appropriate review completion messages with deep links to the content.</p>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * PostReviewedNotification notification = PostReviewedNotification.builder()
 *     .recipients(postCreators)
 *     .reviewerName("Jane Smith")
 *     .postTitle("Summer Campaign Content")
 *     .reviewStatus("APPROVED")
 *     .entityIds(123L, 456L)
 *     .build();
 * </pre>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public class PostReviewedNotification extends BaseNotification {

    // ========================================
    // TYPE-SPECIFIC PROPERTIES
    // ========================================

    /**
     * The name of the reviewer who completed the review.
     */
    @NotBlank(message = "Reviewer name cannot be blank")
    @Size(max = 255, message = "Reviewer name cannot exceed 255 characters")
    private final String reviewerName;

    /**
     * The title or caption of the reviewed post.
     */
    @NotBlank(message = "Post title cannot be blank")
    @Size(max = 500, message = "Post title cannot exceed 500 characters")
    private final String postTitle;

    /**
     * The review status (e.g., "APPROVED", "REJECTED", "NEEDS_CHANGES").
     */
    @NotBlank(message = "Review status cannot be blank")
    @Size(max = 50, message = "Review status cannot exceed 50 characters")
    private final String reviewStatus;

    /**
     * Optional review comments or feedback.
     */
    @Size(max = 1000, message = "Review comments cannot exceed 1000 characters")
    private final String reviewComments;

    /**
     * The ID of the collaboration hub containing the post.
     */
    @NotNull(message = "Hub ID cannot be null")
    private final Long hubId;

    /**
     * The ID of the reviewed post.
     */
    @NotNull(message = "Post ID cannot be null")
    private final Long postId;

    // ========================================
    // CONSTRUCTOR
    // ========================================

    /**
     * Private constructor for builder pattern.
     */
    private PostReviewedNotification(Builder builder) {
        super(builder.recipients, builder.urgency, builder.entityReferences, builder.locale);
        this.reviewerName = builder.reviewerName;
        this.postTitle = builder.postTitle;
        this.reviewStatus = builder.reviewStatus;
        this.reviewComments = builder.reviewComments;
        this.hubId = builder.hubId;
        this.postId = builder.postId;
    }

    // ========================================
    // ABSTRACT METHOD IMPLEMENTATIONS
    // ========================================

    @Override
    public NotificationType getType() {
        return NotificationType.POST_REVIEWED;
    }

    @Override
    protected Map<String, Object> buildParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("reviewerName", reviewerName);
        parameters.put("reviewStatus", reviewStatus);
        if (reviewComments != null && !reviewComments.trim().isEmpty()) {
            parameters.put("reviewComments", reviewComments.trim());
        }
        return parameters;
    }

    @Override
    public NotificationMetadata generateMetadata() {
        NotificationMetadata.Builder builder = NotificationMetadata.builder()
                .actorName(reviewerName)
                .targetTitle(postTitle)
                .actionContext("reviewed your post")
                .reviewStatus(reviewStatus)
                .deepLinkPath(String.format("/app/posts/%d?tab=review", postId));

        if (reviewComments != null && !reviewComments.trim().isEmpty()) {
            builder.commentPreview(reviewComments.trim());
        }

        return builder.build();
    }

    // ========================================
    // VALIDATION
    // ========================================

    @Override
    protected void validateSpecific() {
        if (hubId == null || hubId <= 0) {
            throw new IllegalArgumentException("Hub ID must be a positive number");
        }
        if (postId == null || postId <= 0) {
            throw new IllegalArgumentException("Post ID must be a positive number");
        }
    }

    // ========================================
    // GETTERS
    // ========================================

    public String getReviewerName() {
        return reviewerName;
    }

    public String getPostTitle() {
        return postTitle;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public String getReviewComments() {
        return reviewComments;
    }

    public Long getHubId() {
        return hubId;
    }

    public Long getPostId() {
        return postId;
    }

    // ========================================
    // BUILDER PATTERN
    // ========================================

    /**
     * Creates a new builder for PostReviewedNotification.
     * 
     * @return a new builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for PostReviewedNotification.
     */
    public static class Builder {
        private List<NotificationRecipient> recipients;
        private NotificationUrgency urgency = NotificationUrgency.NORMAL; // Default for reviews
        private NotificationStorageService.EntityReferences entityReferences;
        private Locale locale;
        private String reviewerName;
        private String postTitle;
        private String reviewStatus;
        private String reviewComments;
        private Long hubId;
        private Long postId;

        private Builder() {}

        public Builder recipients(@NotEmpty List<NotificationRecipient> recipients) {
            this.recipients = recipients;
            return this;
        }

        public Builder urgency(@NotNull NotificationUrgency urgency) {
            this.urgency = urgency;
            return this;
        }

        public Builder entityReferences(NotificationStorageService.EntityReferences entityReferences) {
            this.entityReferences = entityReferences;
            return this;
        }

        public Builder locale(Locale locale) {
            this.locale = locale;
            return this;
        }

        public Builder reviewerName(@NotBlank String reviewerName) {
            this.reviewerName = reviewerName;
            return this;
        }

        public Builder postTitle(@NotBlank String postTitle) {
            this.postTitle = postTitle;
            return this;
        }

        public Builder reviewStatus(@NotBlank String reviewStatus) {
            this.reviewStatus = reviewStatus;
            return this;
        }

        public Builder reviewComments(String reviewComments) {
            this.reviewComments = reviewComments;
            return this;
        }

        public Builder hubId(@NotNull Long hubId) {
            this.hubId = hubId;
            return this;
        }

        public Builder postId(@NotNull Long postId) {
            this.postId = postId;
            return this;
        }

        /**
         * Convenience method to set entity references from individual IDs.
         */
        public Builder entityIds(Long hubId, Long postId) {
            this.hubId = hubId;
            this.postId = postId;
            this.entityReferences = NotificationStorageService.EntityReferences.post(hubId, postId);
            return this;
        }

        /**
         * Builds the PostReviewedNotification with validation.
         * 
         * @return a new PostReviewedNotification instance
         * @throws IllegalArgumentException if required fields are missing or invalid
         */
        public PostReviewedNotification build() {
            // Set entity references if not already set
            if (entityReferences == null && hubId != null && postId != null) {
                entityReferences = NotificationStorageService.EntityReferences.post(hubId, postId);
            }

            PostReviewedNotification notification = new PostReviewedNotification(this);
            notification.validate();
            return notification;
        }
    }
}
