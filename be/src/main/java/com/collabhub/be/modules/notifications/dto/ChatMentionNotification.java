package com.collabhub.be.modules.notifications.dto;

import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.service.NotificationTranslationService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Production-grade strongly-typed notification for chat mentions.
 *
 * <p>This notification is sent when a user is mentioned in a chat message.
 * It provides type-safe handling of chat mention context including the mentioner,
 * channel information, and message preview.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Immutable value object with comprehensive validation</li>
 *   <li>Builder pattern for flexible construction</li>
 *   <li>Automatic integration with NotificationTranslationService</li>
 *   <li>Entity references for deep linking to chat channels</li>
 * </ul>
 *
 * <h3>Usage Example:</h3>
 * <pre>
 * ChatMentionNotification notification = ChatMentionNotification.builder()
 *     .recipients(recipients)
 *     .mentionerName("John Doe")
 *     .channelName("General Discussion")
 *     .messagePreview("Hey @jane, what do you think about...")
 *     .entityIds(hubId, channelId)
 *     .locale(Locale.ENGLISH)
 *     .build();
 *
 * notificationDispatcherService.dispatch(notification);
 * </pre>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public final class ChatMentionNotification extends BaseNotification {

    @NotBlank
    @Size(max = 100)
    private final String mentionerName;

    @NotBlank
    @Size(max = 255)
    private final String channelName;

    @NotBlank
    @Size(max = 500)
    private final String messagePreview;

    /**
     * Private constructor for immutable object creation.
     */
    private ChatMentionNotification(Builder builder) {
        super(
            builder.recipients,
            builder.urgency,
            builder.entityReferences,
            builder.locale
        );
        this.mentionerName = builder.mentionerName;
        this.channelName = builder.channelName;
        this.messagePreview = builder.messagePreview;
    }

    /**
     * Creates a new builder for ChatMentionNotification.
     *
     * @return new Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    @Override
    public NotificationType getType() {
        return NotificationType.CHAT_MENTION;
    }

    @Override
    protected Map<String, Object> buildParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("senderName", mentionerName); // Fixed: use senderName to match translation template
        parameters.put("channelName", channelName);
        parameters.put("messagePreview", messagePreview);
        return parameters;
    }

    @Override
    protected void validateSpecific() {
        if (mentionerName == null || mentionerName.trim().isEmpty()) {
            throw new IllegalArgumentException("Mentioner name cannot be null or empty");
        }
        if (channelName == null || channelName.trim().isEmpty()) {
            throw new IllegalArgumentException("Channel name cannot be null or empty");
        }
        if (messagePreview == null || messagePreview.trim().isEmpty()) {
            throw new IllegalArgumentException("Message preview cannot be null or empty");
        }
        if (mentionerName.length() > 100) {
            throw new IllegalArgumentException("Mentioner name exceeds maximum length of 100 characters");
        }
        if (channelName.length() > 255) {
            throw new IllegalArgumentException("Channel name exceeds maximum length of 255 characters");
        }
        if (messagePreview.length() > 500) {
            throw new IllegalArgumentException("Message preview exceeds maximum length of 500 characters");
        }
    }

    // Getters
    public String getMentionerName() {
        return mentionerName;
    }

    public String getChannelName() {
        return channelName;
    }

    public String getMessagePreview() {
        return messagePreview;
    }

    @Override
    public String toString() {
        return String.format("ChatMentionNotification{type=%s, mentionerName='%s', channelName='%s', recipients=%d}",
                           getType(), mentionerName, channelName, getRecipients().size());
    }

    /**
     * Builder class for flexible ChatMentionNotification construction.
     */
    public static final class Builder {
        private List<NotificationRecipient> recipients;
        private String mentionerName;
        private String channelName;
        private String messagePreview;
        private Long hubId;
        private Long channelId;
        private NotificationStorageService.EntityReferences entityReferences;
        private NotificationMetadata metadata;
        private NotificationUrgency urgency = NotificationUrgency.NORMAL;
        private Locale locale = Locale.ENGLISH;

        private Builder() {}

        public Builder recipients(@NotEmpty @Valid List<NotificationRecipient> recipients) {
            this.recipients = recipients;
            return this;
        }

        public Builder mentionerName(@NotBlank String mentionerName) {
            this.mentionerName = mentionerName;
            return this;
        }

        public Builder channelName(@NotBlank String channelName) {
            this.channelName = channelName;
            return this;
        }

        public Builder messagePreview(@NotBlank String messagePreview) {
            this.messagePreview = messagePreview;
            return this;
        }

        public Builder hubId(@NotNull Long hubId) {
            this.hubId = hubId;
            return this;
        }

        public Builder channelId(@NotNull Long channelId) {
            this.channelId = channelId;
            return this;
        }

        public Builder entityReferences(NotificationStorageService.EntityReferences entityReferences) {
            this.entityReferences = entityReferences;
            return this;
        }

        public Builder metadata(@Valid NotificationMetadata metadata) {
            this.metadata = metadata;
            return this;
        }

        public Builder urgency(@NotNull NotificationUrgency urgency) {
            this.urgency = urgency;
            return this;
        }

        public Builder locale(@NotNull Locale locale) {
            this.locale = locale;
            return this;
        }

        /**
         * Convenience method to set entity references from individual IDs.
         */
        public Builder entityIds(Long hubId, Long channelId) {
            this.hubId = hubId;
            this.channelId = channelId;
            this.entityReferences = NotificationStorageService.EntityReferences.chat(hubId, channelId);
            return this;
        }

        /**
         * Builds the ChatMentionNotification with validation.
         * 
         * @return a new ChatMentionNotification instance
         * @throws IllegalArgumentException if required fields are missing or invalid
         */
        public ChatMentionNotification build() {
            // Set entity references if not already set
            if (entityReferences == null && hubId != null && channelId != null) {
                entityReferences = NotificationStorageService.EntityReferences.chat(hubId, channelId);
            }

            ChatMentionNotification notification = new ChatMentionNotification(this);
            notification.validate();
            return notification;
        }
    }
}
