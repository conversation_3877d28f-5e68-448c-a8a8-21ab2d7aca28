package com.collabhub.be.modules.notifications.dto;

import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Strongly-typed notification for reviewer assignments.
 * 
 * <p>This notification is sent when a user is assigned as a reviewer for a post.
 * It provides type-safe properties for all reviewer assignment data and generates
 * appropriate assignment messages with deep links to the content.</p>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * AssignedAsReviewerNotification notification = AssignedAsReviewerNotification.builder()
 *     .recipients(assignedReviewers)
 *     .assignerName("Project Manager")
 *     .postTitle("Summer Campaign Content")
 *     .hubTitle("Summer Campaign 2024")
 *     .entityIds(123L, 456L)
 *     .build();
 * </pre>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public class AssignedAsReviewerNotification extends BaseNotification {

    // ========================================
    // TYPE-SPECIFIC PROPERTIES
    // ========================================

    /**
     * The name of the user who made the reviewer assignment.
     */
    @NotBlank(message = "Assigner name cannot be blank")
    @Size(max = 255, message = "Assigner name cannot exceed 255 characters")
    private final String assignerName;

    /**
     * The title or caption of the post to be reviewed.
     */
    @NotBlank(message = "Post title cannot be blank")
    @Size(max = 500, message = "Post title cannot exceed 500 characters")
    private final String postTitle;

    /**
     * The title of the collaboration hub containing the post.
     */
    @NotBlank(message = "Hub title cannot be blank")
    @Size(max = 500, message = "Hub title cannot exceed 500 characters")
    private final String hubTitle;

    /**
     * Optional deadline for the review.
     */
    @Size(max = 100, message = "Review deadline cannot exceed 100 characters")
    private final String reviewDeadline;

    /**
     * The ID of the collaboration hub containing the post.
     */
    @NotNull(message = "Hub ID cannot be null")
    private final Long hubId;

    /**
     * The ID of the post to be reviewed.
     */
    @NotNull(message = "Post ID cannot be null")
    private final Long postId;

    // ========================================
    // CONSTRUCTOR
    // ========================================

    /**
     * Private constructor for builder pattern.
     */
    private AssignedAsReviewerNotification(Builder builder) {
        super(builder.recipients, builder.urgency, builder.entityReferences, builder.locale);
        this.assignerName = builder.assignerName;
        this.postTitle = builder.postTitle;
        this.hubTitle = builder.hubTitle;
        this.reviewDeadline = builder.reviewDeadline;
        this.hubId = builder.hubId;
        this.postId = builder.postId;
    }

    // ========================================
    // ABSTRACT METHOD IMPLEMENTATIONS
    // ========================================

    @Override
    public NotificationType getType() {
        return NotificationType.ASSIGNED_AS_REVIEWER;
    }

    @Override
    protected Map<String, Object> buildParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("assignerName", assignerName);
        parameters.put("hubTitle", hubTitle);
        if (reviewDeadline != null && !reviewDeadline.trim().isEmpty()) {
            parameters.put("reviewDeadline", reviewDeadline.trim());
        }
        return parameters;
    }

    @Override
    public NotificationMetadata generateMetadata() {
        NotificationMetadata.Builder builder = NotificationMetadata.builder()
                .actorName(assignerName)
                .targetTitle(postTitle)
                .actionContext("assigned you to review content")
                .deepLinkPath(String.format("/app/posts/%d?tab=review", postId));

        if (reviewDeadline != null && !reviewDeadline.trim().isEmpty()) {
            builder.urgencyReason("Review deadline: " + reviewDeadline.trim());
        }

        return builder.build();
    }

    // ========================================
    // VALIDATION
    // ========================================

    @Override
    protected void validateSpecific() {
        if (hubId == null || hubId <= 0) {
            throw new IllegalArgumentException("Hub ID must be a positive number");
        }
        if (postId == null || postId <= 0) {
            throw new IllegalArgumentException("Post ID must be a positive number");
        }
    }

    // ========================================
    // GETTERS
    // ========================================

    public String getAssignerName() {
        return assignerName;
    }

    public String getPostTitle() {
        return postTitle;
    }

    public String getHubTitle() {
        return hubTitle;
    }

    public String getReviewDeadline() {
        return reviewDeadline;
    }

    public Long getHubId() {
        return hubId;
    }

    public Long getPostId() {
        return postId;
    }

    // ========================================
    // BUILDER PATTERN
    // ========================================

    /**
     * Creates a new builder for AssignedAsReviewerNotification.
     * 
     * @return a new builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for AssignedAsReviewerNotification.
     */
    public static class Builder {
        private List<NotificationRecipient> recipients;
        private NotificationUrgency urgency = NotificationUrgency.HIGH; // Default for assignments
        private NotificationStorageService.EntityReferences entityReferences;
        private Locale locale;
        private String assignerName;
        private String postTitle;
        private String hubTitle;
        private String reviewDeadline;
        private Long hubId;
        private Long postId;

        private Builder() {}

        public Builder recipients(@NotEmpty List<NotificationRecipient> recipients) {
            this.recipients = recipients;
            return this;
        }

        public Builder urgency(@NotNull NotificationUrgency urgency) {
            this.urgency = urgency;
            return this;
        }

        public Builder entityReferences(NotificationStorageService.EntityReferences entityReferences) {
            this.entityReferences = entityReferences;
            return this;
        }

        public Builder locale(Locale locale) {
            this.locale = locale;
            return this;
        }

        public Builder assignerName(@NotBlank String assignerName) {
            this.assignerName = assignerName;
            return this;
        }

        public Builder postTitle(@NotBlank String postTitle) {
            this.postTitle = postTitle;
            return this;
        }

        public Builder hubTitle(@NotBlank String hubTitle) {
            this.hubTitle = hubTitle;
            return this;
        }

        public Builder reviewDeadline(String reviewDeadline) {
            this.reviewDeadline = reviewDeadline;
            return this;
        }

        public Builder hubId(@NotNull Long hubId) {
            this.hubId = hubId;
            return this;
        }

        public Builder postId(@NotNull Long postId) {
            this.postId = postId;
            return this;
        }

        /**
         * Convenience method to set entity references from individual IDs.
         */
        public Builder entityIds(Long hubId, Long postId) {
            this.hubId = hubId;
            this.postId = postId;
            this.entityReferences = NotificationStorageService.EntityReferences.post(hubId, postId);
            return this;
        }

        /**
         * Builds the AssignedAsReviewerNotification with validation.
         * 
         * @return a new AssignedAsReviewerNotification instance
         * @throws IllegalArgumentException if required fields are missing or invalid
         */
        public AssignedAsReviewerNotification build() {
            // Set entity references if not already set
            if (entityReferences == null && hubId != null && postId != null) {
                entityReferences = NotificationStorageService.EntityReferences.post(hubId, postId);
            }

            AssignedAsReviewerNotification notification = new AssignedAsReviewerNotification(this);
            notification.validate();
            return notification;
        }
    }
}
