package com.collabhub.be.modules.notifications.util;

import com.collabhub.be.modules.notifications.dto.ExternalUserRecipient;
import com.collabhub.be.modules.notifications.dto.InternalUserRecipient;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.User;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Production-grade utility class for notification recipient management.
 *
 * <p>This utility class provides factory methods and conversion utilities for working
 * with notification recipients in the unified notification system. It handles the
 * complexity of converting between different data sources and recipient types.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Type-safe conversion from various data sources</li>
 *   <li>Bulk operations for performance optimization</li>
 *   <li>Validation and error handling</li>
 *   <li>Recipient filtering and grouping utilities</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public final class NotificationRecipientUtils {

    private NotificationRecipientUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    // ========================================
    // FACTORY METHODS
    // ========================================

    /**
     * Creates a NotificationRecipient from a User entity.
     *
     * @param user the user entity (must not be null)
     * @return InternalUserRecipient instance
     * @throws IllegalArgumentException if user is invalid
     */
    public static InternalUserRecipient fromUser(@NotNull User user) {
        return InternalUserRecipient.fromUser(user);
    }

    /**
     * Creates a NotificationRecipient from a HubParticipant entity.
     *
     * <p>Automatically determines if the participant is internal (has user_id) or
     * external (email-only) and creates the appropriate recipient type.</p>
     *
     * @param participant the hub participant (must not be null)
     * @return NotificationRecipient instance (Internal or External)
     * @throws IllegalArgumentException if participant is invalid
     */
    public static NotificationRecipient fromHubParticipant(@NotNull HubParticipant participant) {
        if (participant.getUserId() != null) {
            // Internal participant - need to get user data
            throw new IllegalArgumentException("Internal participants require User entity data. Use fromHubParticipantWithUser() instead.");
        } else {
            // External participant
            return ExternalUserRecipient.fromHubParticipant(participant);
        }
    }

    /**
     * Creates a NotificationRecipient from a HubParticipant with User data.
     *
     * <p>This method should be used when you have both HubParticipant and User data
     * for internal participants, ensuring complete recipient information.</p>
     *
     * @param participant the hub participant (must not be null)
     * @param user the user entity (must not be null for internal participants)
     * @return NotificationRecipient instance
     * @throws IllegalArgumentException if data is inconsistent
     */
    public static NotificationRecipient fromHubParticipantWithUser(@NotNull HubParticipant participant, 
                                                                  @NotNull User user) {
        if (participant.getUserId() == null) {
            // External participant - ignore user data
            return ExternalUserRecipient.fromHubParticipant(participant);
        } else {
            // Internal participant - validate consistency
            if (!participant.getUserId().equals(user.getId())) {
                throw new IllegalArgumentException("HubParticipant user_id does not match User id");
            }
            return InternalUserRecipient.fromUser(user);
        }
    }

    // ========================================
    // BULK CONVERSION METHODS
    // ========================================

    /**
     * Converts a list of User entities to NotificationRecipients.
     *
     * @param users the list of users (must not be null or empty)
     * @return list of InternalUserRecipient instances
     * @throws IllegalArgumentException if any user is invalid
     */
    public static List<InternalUserRecipient> fromUsers(@NotEmpty List<User> users) {
        return users.stream()
                   .map(InternalUserRecipient::fromUser)
                   .collect(Collectors.toList());
    }

    /**
     * Converts a list of HubParticipant entities to NotificationRecipients.
     *
     * <p>This method handles mixed participants (both internal and external) but
     * requires that internal participants have complete user data available.</p>
     *
     * @param participants the list of participants (must not be null or empty)
     * @return list of NotificationRecipient instances (mixed types)
     * @throws IllegalArgumentException if any participant is invalid or internal participants lack user data
     */
    public static List<NotificationRecipient> fromHubParticipants(@NotEmpty List<HubParticipant> participants) {
        List<NotificationRecipient> recipients = new ArrayList<>();

        for (HubParticipant participant : participants) {
            if (participant.getUserId() != null) {
                throw new IllegalArgumentException("Internal participants require User entity data. Use convertHubParticipantsToRecipients() instead.");
            } else {
                recipients.add(ExternalUserRecipient.fromHubParticipant(participant));
            }
        }

        return recipients;
    }

    /**
     * Converts a list of HubParticipant entities to NotificationRecipients with User lookup.
     *
     * <p>This method handles mixed participants (both internal and external) and
     * performs bulk User entity loading for internal participants. This is the preferred
     * method for converting hub participants in service classes.</p>
     *
     * @param participants the list of participants (must not be null or empty)
     * @param userRepository the user repository for bulk loading internal users
     * @return list of NotificationRecipient instances (mixed types)
     * @throws IllegalArgumentException if any participant is invalid
     */
    public static List<NotificationRecipient> convertHubParticipantsToRecipients(
            @NotEmpty List<HubParticipant> participants,
            @NotNull com.collabhub.be.modules.auth.repository.UserRepository userRepository) {

        if (participants.isEmpty()) {
            return List.of();
        }

        List<NotificationRecipient> recipients = new ArrayList<>();
        List<Long> internalUserIds = new ArrayList<>();

        // Separate internal and external participants
        for (HubParticipant participant : participants) {
            if (participant.getUserId() != null) {
                // Internal participant - collect user ID for bulk loading
                internalUserIds.add(participant.getUserId());
            } else {
                // External participant - create recipient directly
                recipients.add(ExternalUserRecipient.fromHubParticipant(participant));
            }
        }

        // Bulk load internal users if any
        if (!internalUserIds.isEmpty()) {
            List<org.jooq.generated.tables.pojos.User> users = userRepository.findByIds(internalUserIds);
            recipients.addAll(users.stream()
                                  .map(InternalUserRecipient::fromUser)
                                  .collect(Collectors.toList()));
        }

        return recipients;
    }

    // ========================================
    // RECIPIENT FILTERING AND GROUPING
    // ========================================

    /**
     * Separates a mixed list of recipients into internal users only.
     *
     * @param recipients the mixed recipient list (must not be null)
     * @return list of internal user recipients
     */
    public static List<InternalUserRecipient> filterInternalUsers(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .filter(NotificationRecipient::isInternal)
                        .map(r -> (InternalUserRecipient) r)
                        .collect(Collectors.toList());
    }

    /**
     * Separates a mixed list of recipients into external users only.
     *
     * @param recipients the mixed recipient list (must not be null)
     * @return list of external user recipients
     */
    public static List<ExternalUserRecipient> filterExternalUsers(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .filter(r -> !r.isInternal())
                        .map(r -> (ExternalUserRecipient) r)
                        .collect(Collectors.toList());
    }

    /**
     * Extracts user IDs from internal user recipients.
     *
     * @param recipients the recipient list (must not be null)
     * @return set of user IDs from internal recipients
     */
    public static Set<Long> extractUserIds(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .filter(NotificationRecipient::isInternal)
                        .map(NotificationRecipient::getUserId)
                        .collect(Collectors.toSet());
    }

    /**
     * Extracts email addresses from all recipients.
     *
     * @param recipients the recipient list (must not be null)
     * @return set of email addresses from all recipients
     */
    public static Set<String> extractEmails(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .map(NotificationRecipient::getEmail)
                        .collect(Collectors.toSet());
    }

    /**
     * Extracts email addresses from external user recipients only.
     *
     * @param recipients the recipient list (must not be null)
     * @return set of email addresses from external recipients
     */
    public static Set<String> extractExternalEmails(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .filter(r -> !r.isInternal())
                        .map(NotificationRecipient::getEmail)
                        .collect(Collectors.toSet());
    }

    // ========================================
    // VALIDATION AND UTILITY METHODS
    // ========================================

    /**
     * Validates a list of recipients, ensuring all are properly formed.
     *
     * @param recipients the recipient list to validate (must not be null)
     * @throws IllegalArgumentException if any recipient is invalid
     */
    public static void validateRecipients(@NotNull List<NotificationRecipient> recipients) {
        if (recipients.isEmpty()) {
            throw new IllegalArgumentException("Recipient list cannot be empty");
        }
        
        for (int i = 0; i < recipients.size(); i++) {
            NotificationRecipient recipient = recipients.get(i);
            if (recipient == null) {
                throw new IllegalArgumentException("Recipient at index " + i + " is null");
            }
            
            try {
                recipient.validate();
            } catch (Exception e) {
                throw new IllegalArgumentException("Recipient at index " + i + " is invalid: " + e.getMessage(), e);
            }
        }
    }

    /**
     * Removes duplicate recipients from a list based on unique identifiers.
     *
     * @param recipients the recipient list (must not be null)
     * @return deduplicated list maintaining original order
     */
    public static List<NotificationRecipient> deduplicateRecipients(@NotNull List<NotificationRecipient> recipients) {
        return recipients.stream()
                        .collect(Collectors.toMap(
                            NotificationRecipient::getUniqueIdentifier,
                            r -> r,
                            (existing, replacement) -> existing, // Keep first occurrence
                            java.util.LinkedHashMap::new // Maintain order
                        ))
                        .values()
                        .stream()
                        .collect(Collectors.toList());
    }

    /**
     * Creates a summary string for logging purposes.
     *
     * @param recipients the recipient list (must not be null)
     * @return summary string with counts and types
     */
    public static String createSummary(@NotNull List<NotificationRecipient> recipients) {
        long internalCount = recipients.stream().filter(NotificationRecipient::isInternal).count();
        long externalCount = recipients.size() - internalCount;
        
        return String.format("Recipients[total=%d, internal=%d, external=%d]", 
                           recipients.size(), internalCount, externalCount);
    }
}
