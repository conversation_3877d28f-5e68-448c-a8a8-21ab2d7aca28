package com.collabhub.be.modules.notifications.dto;

import com.collabhub.be.modules.notifications.service.NotificationStorageService;
import com.collabhub.be.modules.notifications.dto.NotificationRecipient;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Strongly-typed notification for comment additions.
 * 
 * <p>This notification is sent when a comment is added to a post that users are
 * involved with (as creators, reviewers, or previous commenters). It provides
 * type-safe properties for all comment-related data.</p>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * CommentAddedNotification notification = CommentAddedNotification.builder()
 *     .recipients(interestedParticipants)
 *     .commenterName("John Doe")
 *     .postTitle("Summer Campaign")
 *     .commentPreview("This looks great!")
 *     .entityIds(123L, 456L, 789L)
 *     .build();
 * </pre>
 * 
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
public class CommentAddedNotification extends BaseNotification {

    // ========================================
    // TYPE-SPECIFIC PROPERTIES
    // ========================================

    /**
     * The name of the user who added the comment.
     */
    @NotBlank(message = "Commenter name cannot be blank")
    @Size(max = 255, message = "Commenter name cannot exceed 255 characters")
    private final String commenterName;

    /**
     * The title or caption of the post where the comment was added.
     */
    @NotBlank(message = "Post title cannot be blank")
    @Size(max = 500, message = "Post title cannot exceed 500 characters")
    private final String postTitle;

    /**
     * A preview of the comment content.
     */
    @NotBlank(message = "Comment preview cannot be blank")
    @Size(max = 200, message = "Comment preview cannot exceed 200 characters")
    private final String commentPreview;

    /**
     * The ID of the collaboration hub where the comment was added.
     */
    @NotNull(message = "Hub ID cannot be null")
    private final Long hubId;

    /**
     * The ID of the post where the comment was added.
     */
    @NotNull(message = "Post ID cannot be null")
    private final Long postId;

    /**
     * The ID of the newly added comment.
     */
    @NotNull(message = "Comment ID cannot be null")
    private final Long commentId;

    // ========================================
    // CONSTRUCTOR
    // ========================================

    /**
     * Private constructor for builder pattern.
     */
    private CommentAddedNotification(Builder builder) {
        super(builder.recipients, builder.urgency, builder.entityReferences, builder.locale);
        this.commenterName = builder.commenterName;
        this.postTitle = builder.postTitle;
        this.commentPreview = builder.commentPreview;
        this.hubId = builder.hubId;
        this.postId = builder.postId;
        this.commentId = builder.commentId;
    }

    // ========================================
    // ABSTRACT METHOD IMPLEMENTATIONS
    // ========================================

    @Override
    public NotificationType getType() {
        return NotificationType.COMMENT_ADDED;
    }

    @Override
    protected Map<String, Object> buildParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("commenterName", commenterName);
        parameters.put("commentPreview", commentPreview);
        return parameters;
    }

    @Override
    public NotificationMetadata generateMetadata() {
        return NotificationMetadata.builder()
                .actorName(commenterName)
                .targetTitle(postTitle)
                .actionContext("commented on a post")
                .commentPreview(commentPreview)
                .deepLinkPath(String.format("/app/posts/%d#comment-%d", postId, commentId))
                .build();
    }

    // ========================================
    // VALIDATION
    // ========================================

    @Override
    protected void validateSpecific() {
        if (hubId == null || hubId <= 0) {
            throw new IllegalArgumentException("Hub ID must be a positive number");
        }
        if (postId == null || postId <= 0) {
            throw new IllegalArgumentException("Post ID must be a positive number");
        }
        if (commentId == null || commentId <= 0) {
            throw new IllegalArgumentException("Comment ID must be a positive number");
        }
    }

    // ========================================
    // GETTERS
    // ========================================

    public String getCommenterName() {
        return commenterName;
    }

    public String getPostTitle() {
        return postTitle;
    }

    public String getCommentPreview() {
        return commentPreview;
    }

    public Long getHubId() {
        return hubId;
    }

    public Long getPostId() {
        return postId;
    }

    public Long getCommentId() {
        return commentId;
    }

    // ========================================
    // BUILDER PATTERN
    // ========================================

    /**
     * Creates a new builder for CommentAddedNotification.
     * 
     * @return a new builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for CommentAddedNotification.
     */
    public static class Builder {
        private List<NotificationRecipient> recipients;
        private NotificationUrgency urgency = NotificationUrgency.LOW; // Default for general comments
        private NotificationStorageService.EntityReferences entityReferences;
        private Locale locale;
        private String commenterName;
        private String postTitle;
        private String commentPreview;
        private Long hubId;
        private Long postId;
        private Long commentId;

        private Builder() {}

        public Builder recipients(@NotEmpty List<NotificationRecipient> recipients) {
            this.recipients = recipients;
            return this;
        }

        public Builder urgency(@NotNull NotificationUrgency urgency) {
            this.urgency = urgency;
            return this;
        }

        public Builder entityReferences(NotificationStorageService.EntityReferences entityReferences) {
            this.entityReferences = entityReferences;
            return this;
        }

        public Builder locale(Locale locale) {
            this.locale = locale;
            return this;
        }

        public Builder commenterName(@NotBlank String commenterName) {
            this.commenterName = commenterName;
            return this;
        }

        public Builder postTitle(@NotBlank String postTitle) {
            this.postTitle = postTitle;
            return this;
        }

        public Builder commentPreview(@NotBlank String commentPreview) {
            this.commentPreview = commentPreview;
            return this;
        }

        public Builder hubId(@NotNull Long hubId) {
            this.hubId = hubId;
            return this;
        }

        public Builder postId(@NotNull Long postId) {
            this.postId = postId;
            return this;
        }

        public Builder commentId(@NotNull Long commentId) {
            this.commentId = commentId;
            return this;
        }

        /**
         * Convenience method to set entity references from individual IDs.
         */
        public Builder entityIds(Long hubId, Long postId, Long commentId) {
            this.hubId = hubId;
            this.postId = postId;
            this.commentId = commentId;
            this.entityReferences = NotificationStorageService.EntityReferences.comment(hubId, postId, commentId);
            return this;
        }

        /**
         * Builds the CommentAddedNotification with validation.
         * 
         * @return a new CommentAddedNotification instance
         * @throws IllegalArgumentException if required fields are missing or invalid
         */
        public CommentAddedNotification build() {
            // Set entity references if not already set
            if (entityReferences == null && hubId != null && postId != null && commentId != null) {
                entityReferences = NotificationStorageService.EntityReferences.comment(hubId, postId, commentId);
            }

            CommentAddedNotification notification = new CommentAddedNotification(this);
            notification.validate();
            return notification;
        }
    }
}
